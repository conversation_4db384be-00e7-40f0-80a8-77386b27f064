import 'package:supabase_flutter/supabase_flutter.dart' hide AuthException;
import 'package:meditatingleo_admin/features/auth/data/models/admin_user_model.dart';
import 'package:meditatingleo_admin/features/auth/data/models/auth_response_model.dart';
import 'package:meditatingleo_admin/core/error/exceptions.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// Service for handling admin authentication operations.
///
/// Provides secure authentication for admin users with role-based access control.
/// Supports MFA requirements for elevated admin roles.
class AdminAuthService {
  /// Creates an [AdminAuthService] instance.
  const AdminAuthService(this._supabaseClient);

  final SupabaseClient _supabaseClient;

  /// Signs in an admin user with email and password.
  ///
  /// Returns [AuthResponseModel] with user information and tokens.
  /// Throws AuthException if authentication fails or user is not an admin.
  Future<AuthResponseModel> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      final response = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null || response.session == null) {
        throw const AuthException('Authentication failed');
      }

      final user = response.user!;
      final session = response.session!;

      // Validate admin role
      if (!isValidAdminRole(user.userMetadata ?? {})) {
        await _supabaseClient.auth.signOut();
        throw const AuthException('Access denied: Admin privileges required');
      }

      // Check if account is active
      final isActive = user.userMetadata?['is_active'] as bool? ?? false;
      if (!isActive) {
        await _supabaseClient.auth.signOut();
        throw const AuthException('Account is inactive');
      }

      final adminUser = _createAdminUserFromSupabaseUser(user);

      // Check if MFA is required for this role
      final requiresMfa = _shouldRequireMfa(adminUser.role, user.userMetadata);
      String? mfaToken;

      if (requiresMfa) {
        // Generate MFA challenge token
        mfaToken = _generateMfaToken(user.id);
      }

      return AuthResponseModel(
        user: adminUser,
        accessToken: session.accessToken,
        refreshToken: session.refreshToken ?? '',
        expiresAt: DateTime.fromMillisecondsSinceEpoch(
          (session.expiresAt ?? 0) * 1000,
        ).toIso8601String(),
        requiresMfa: requiresMfa,
        mfaToken: mfaToken,
      );
    } on AuthException {
      rethrow;
    } catch (e) {
      throw AuthException('Authentication failed: ${e.toString()}');
    }
  }

  /// Signs out the current admin user.
  Future<void> signOut() async {
    try {
      await _supabaseClient.auth.signOut();
    } catch (e) {
      throw AuthException('Sign out failed: ${e.toString()}');
    }
  }

  /// Gets the current authenticated admin user.
  ///
  /// Returns [AdminUserModel] if user is authenticated and is an admin.
  /// Returns null if no user is authenticated or user is not an admin.
  Future<AdminUserModel?> getCurrentUser() async {
    try {
      final user = _supabaseClient.auth.currentUser;

      if (user == null) {
        return null;
      }

      // Validate admin role
      if (!isValidAdminRole(user.userMetadata ?? {})) {
        return null;
      }

      return _createAdminUserFromSupabaseUser(user);
    } catch (e) {
      return null;
    }
  }

  /// Refreshes the current session.
  ///
  /// Returns updated [AuthResponseModel] with new tokens.
  /// Throws [AuthException] if refresh fails.
  Future<AuthResponseModel> refreshSession() async {
    try {
      final response = await _supabaseClient.auth.refreshSession();

      if (response.session == null) {
        throw const AuthException('Session refresh failed');
      }

      final session = response.session!;
      final user = _supabaseClient.auth.currentUser;

      if (user == null) {
        throw const AuthException('No authenticated user');
      }

      final adminUser = _createAdminUserFromSupabaseUser(user);

      return AuthResponseModel(
        user: adminUser,
        accessToken: session.accessToken,
        refreshToken: session.refreshToken ?? '',
        expiresAt: DateTime.fromMillisecondsSinceEpoch(
          (session.expiresAt ?? 0) * 1000,
        ).toIso8601String(),
        requiresMfa: false, // Already authenticated
        mfaToken: null,
      );
    } catch (e) {
      throw AuthException('Session refresh failed: ${e.toString()}');
    }
  }

  /// Changes the password for the current admin user.
  ///
  /// Throws [AuthException] if password change fails.
  Future<void> changePassword(String newPassword) async {
    try {
      await _supabaseClient.auth.updateUser(
        UserAttributes(password: newPassword),
      );
    } catch (e) {
      throw AuthException('Password change failed: ${e.toString()}');
    }
  }

  /// Validates if the user metadata contains a valid admin role.
  bool isValidAdminRole(Map<String, dynamic> metadata) {
    final role = metadata['role'] as String?;
    return role == 'content_manager' ||
        role == 'system_admin' ||
        role == 'super_admin';
  }

  /// Creates an [AdminUserModel] from a Supabase [User].
  AdminUserModel _createAdminUserFromSupabaseUser(User user) {
    final metadata = user.userMetadata ?? {};

    AdminRole parseRole(String? roleString) {
      switch (roleString) {
        case 'content_manager':
          return AdminRole.contentManager;
        case 'system_admin':
          return AdminRole.systemAdmin;
        case 'super_admin':
          return AdminRole.superAdmin;
        default:
          return AdminRole.contentManager;
      }
    }

    return AdminUserModel(
      id: user.id,
      email: user.email ?? '',
      role: parseRole(metadata['role'] as String?),
      isActive: metadata['is_active'] as bool? ?? false,
      lastLoginAt: metadata['last_login_at'] as String?,
      createdAt:
          metadata['created_at'] as String? ?? DateTime.now().toIso8601String(),
      updatedAt:
          metadata['updated_at'] as String? ?? DateTime.now().toIso8601String(),
    );
  }

  /// Determines if MFA should be required for the given role and metadata.
  bool _shouldRequireMfa(AdminRole role, Map<String, dynamic>? metadata) {
    // Content managers don't require MFA
    if (role == AdminRole.contentManager) {
      return false;
    }

    // System admins and super admins require MFA if enabled
    final mfaEnabled = metadata?['mfa_enabled'] as bool? ?? false;
    return mfaEnabled;
  }

  /// Generates an MFA challenge token for the user.
  String _generateMfaToken(String userId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final data = '$userId:$timestamp';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
