// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_auth_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$adminAuthServiceHash() => r'b73a5d016fc75d420bb59fcf61ebdb9e96439ff7';

/// Provider for admin authentication service
///
/// Copied from [adminAuthService].
@ProviderFor(adminAuthService)
final adminAuthServiceProvider = AutoDisposeProvider<AdminAuthService>.internal(
  adminAuthService,
  name: r'adminAuthServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$adminAuthServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AdminAuthServiceRef = AutoDisposeProviderRef<AdminAuthService>;
String _$auditLogServiceHash() => r'2f9ec601378e718402f5a6497856efa07553c382';

/// Provider for audit log service
///
/// Copied from [auditLogService].
@ProviderFor(auditLogService)
final auditLogServiceProvider = AutoDisposeProvider<AuditLogService>.internal(
  auditLogService,
  name: r'auditLogServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$auditLogServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuditLogServiceRef = AutoDisposeProviderRef<AuditLogService>;
String _$secureStorageServiceHash() =>
    r'49b4edfbfe150d06138d563b7153f1c007c8def7';

/// Provider for secure storage service
///
/// Copied from [secureStorageService].
@ProviderFor(secureStorageService)
final secureStorageServiceProvider =
    AutoDisposeProvider<SecureStorageService>.internal(
  secureStorageService,
  name: r'secureStorageServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$secureStorageServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SecureStorageServiceRef = AutoDisposeProviderRef<SecureStorageService>;
String _$adminAuthNotifierHash() => r'91d21cdf604c5bc7062c23e9a0cbe6f7d0054b17';

/// Main authentication state notifier
///
/// Copied from [AdminAuthNotifier].
@ProviderFor(AdminAuthNotifier)
final adminAuthNotifierProvider = AutoDisposeAsyncNotifierProvider<
    AdminAuthNotifier, AdminAuthState>.internal(
  AdminAuthNotifier.new,
  name: r'adminAuthNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$adminAuthNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AdminAuthNotifier = AutoDisposeAsyncNotifier<AdminAuthState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
