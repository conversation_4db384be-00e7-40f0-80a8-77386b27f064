import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:meditatingleo_admin/app/router/routes.dart';
import 'package:meditatingleo_admin/features/auth/presentation/providers/mfa_provider.dart';
import 'package:meditatingleo_admin/features/auth/presentation/providers/admin_auth_provider.dart';

/// MFA verification page for completing two-factor authentication.
///
/// Provides TOTP code input and backup code verification with modern
/// Material Design 3 styling and proper accessibility support.
class MfaVerificationPage extends ConsumerStatefulWidget {
  const MfaVerificationPage({
    super.key,
    required this.userId,
    required this.mfaToken,
  });

  final String userId;
  final String mfaToken;

  static const String routeName = '/admin/mfa-verification';

  @override
  ConsumerState<MfaVerificationPage> createState() =>
      _MfaVerificationPageState();
}

class _MfaVerificationPageState extends ConsumerState<MfaVerificationPage> {
  final _codeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _useBackupCode = false;

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isVerifying = ref.watch(mfaVerificationNotifierProvider);

    // Listen for authentication completion
    ref.listen<AsyncValue<AdminAuthState>>(
      adminAuthNotifierProvider,
      (previous, next) {
        next.when(
          data: (state) {
            if (state.isAuthenticated && !state.requiresMfa) {
              // Navigate to main admin dashboard
              context.go(AppRoutes.dashboard);
            }
          },
          loading: () {},
          error: (error, stackTrace) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Verification failed: ${error.toString()}'),
                backgroundColor: Theme.of(context).colorScheme.error,
                behavior: SnackBarBehavior.floating,
              ),
            );
          },
        );
      },
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Two-Factor Authentication'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Sign out and return to login
            ref.read(adminAuthNotifierProvider.notifier).signOut();
            context.go(AppRoutes.login);
          },
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context)
                  .colorScheme
                  .primaryContainer
                  .withValues(alpha: 0.3),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // MFA icon
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        Icons.security,
                        size: 40,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),

                    const SizedBox(height: 24),

                    Text(
                      'Verify Your Identity',
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    Text(
                      _useBackupCode
                          ? 'Enter one of your backup codes'
                          : 'Enter the 6-digit code from your authenticator app',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 32),

                    // Verification form
                    Card.filled(
                      child: Padding(
                        padding: const EdgeInsets.all(24.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              // Code input field
                              TextFormField(
                                controller: _codeController,
                                keyboardType: _useBackupCode
                                    ? TextInputType.text
                                    : TextInputType.number,
                                textAlign: TextAlign.center,
                                style: Theme.of(context)
                                    .textTheme
                                    .headlineMedium
                                    ?.copyWith(
                                      letterSpacing: 8,
                                      fontWeight: FontWeight.bold,
                                    ),
                                inputFormatters: _useBackupCode
                                    ? [
                                        LengthLimitingTextInputFormatter(8),
                                        FilteringTextInputFormatter.allow(
                                            RegExp(r'[0-9a-zA-Z]')),
                                      ]
                                    : [
                                        LengthLimitingTextInputFormatter(6),
                                        FilteringTextInputFormatter.digitsOnly,
                                      ],
                                decoration: InputDecoration(
                                  hintText:
                                      _useBackupCode ? 'XXXXXXXX' : '000000',
                                  hintStyle: TextStyle(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant
                                        .withValues(alpha: 0.5),
                                    letterSpacing: 8,
                                  ),
                                  border: const OutlineInputBorder(),
                                  filled: true,
                                  fillColor: Theme.of(context)
                                      .colorScheme
                                      .surfaceContainerHighest,
                                  contentPadding: const EdgeInsets.symmetric(
                                    vertical: 20,
                                    horizontal: 16,
                                  ),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter the verification code';
                                  }
                                  if (_useBackupCode) {
                                    if (value.length < 6) {
                                      return 'Backup code must be at least 6 characters';
                                    }
                                  } else {
                                    if (value.length != 6) {
                                      return 'Code must be exactly 6 digits';
                                    }
                                  }
                                  return null;
                                },
                                onFieldSubmitted: (_) => _handleVerification(),
                              ),

                              const SizedBox(height: 24),

                              // Verify button
                              FilledButton(
                                onPressed:
                                    isVerifying ? null : _handleVerification,
                                style: FilledButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: isVerifying
                                    ? SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                            Theme.of(context)
                                                .colorScheme
                                                .onPrimary,
                                          ),
                                        ),
                                      )
                                    : Text(
                                        'Verify',
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelLarge
                                            ?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                      ),
                              ),

                              const SizedBox(height: 16),

                              // Toggle backup code
                              TextButton(
                                onPressed:
                                    isVerifying ? null : _toggleBackupCode,
                                child: Text(
                                  _useBackupCode
                                      ? 'Use authenticator app instead'
                                      : 'Use backup code instead',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Help text
                    Card.outlined(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 20,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'Having trouble?',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _useBackupCode
                                  ? 'Backup codes are single-use codes that were provided when you set up two-factor authentication.'
                                  : 'Open your authenticator app and enter the 6-digit code. The code refreshes every 30 seconds.',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _toggleBackupCode() {
    setState(() {
      _useBackupCode = !_useBackupCode;
      _codeController.clear();
    });
  }

  Future<void> _handleVerification() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Hide keyboard
    FocusScope.of(context).unfocus();

    try {
      final success = await ref
          .read(mfaVerificationNotifierProvider.notifier)
          .verifyLoginMfa(widget.userId, _codeController.text.trim());

      if (!success) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _useBackupCode
                    ? 'Invalid backup code. Please try again.'
                    : 'Invalid verification code. Please try again.',
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
            ),
          );

          // Clear the input
          _codeController.clear();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Verification failed: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
