import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'app/app.dart';
import 'core/constants/app_constants.dart';

/// Main entry point for MeditatingLeo Admin Panel
///
/// This function initializes the Flutter application with:
/// - Environment variables loading
/// - Supabase configuration
/// - Desktop window management
/// - Riverpod state management
/// - Error handling
///
/// Following Flutter 2025+ standards with proper initialization sequence.
void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Load environment variables
    await _loadEnvironmentVariables();

    // Initialize Supabase
    await _initializeSupabase();

    // Run the application
    runApp(
      const ProviderScope(
        child: AdminApp(),
      ),
    );
  } catch (error, stackTrace) {
    // Handle initialization errors
    _handleInitializationError(error, stackTrace);
  }
}

/// Load environment variables from .env file
Future<void> _loadEnvironmentVariables() async {
  try {
    await dotenv.load(fileName: '.env');
    debugPrint('Environment variables loaded successfully');
    debugPrint(
        'SUPABASE_URL: ${dotenv.env['SUPABASE_URL']?.substring(0, 20)}...');
  } catch (e) {
    debugPrint('Warning: Could not load .env file: $e');
    // Continue without .env file - will use compile-time environment variables
  }
}

/// Initialize Supabase with admin configuration
Future<void> _initializeSupabase() async {
  // Get Supabase configuration from environment variables
  final supabaseUrl = dotenv.env['SUPABASE_URL'] ??
      const String.fromEnvironment('SUPABASE_URL', defaultValue: '');
  final supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'] ??
      const String.fromEnvironment('SUPABASE_ANON_KEY', defaultValue: '');

  // Validate configuration
  if (supabaseUrl.isEmpty || supabaseAnonKey.isEmpty) {
    throw Exception(
        'Supabase configuration missing. Please check your .env file or environment variables.\n'
        'Required: SUPABASE_URL, SUPABASE_ANON_KEY');
  }

  debugPrint('Initializing Supabase with URL: $supabaseUrl');

  await Supabase.initialize(
    url: supabaseUrl,
    anonKey: supabaseAnonKey,
    debug: AppConstants.isDevelopment,
  );

  debugPrint('Supabase initialized successfully');

  // Test database connection
  await _testDatabaseConnection();
}

/// Test database connection to verify Supabase is working
Future<void> _testDatabaseConnection() async {
  try {
    final client = Supabase.instance.client;

    // Test a simple query to check connection
    // This will try to query the auth.users table which should always exist
    final response = await client.auth.getUser();
    debugPrint('Database connection test: SUCCESS');
    debugPrint(
        'Auth service accessible: ${response.user == null ? 'No user' : 'User exists'}');

    // Test if we can access any tables (this might fail if tables don't exist, which is fine)
    try {
      await client.from('profiles').select('count').limit(1);
      debugPrint('Profiles table accessible: SUCCESS');
    } catch (e) {
      debugPrint('Profiles table test: ${e.toString().substring(0, 50)}...');
    }
  } catch (e) {
    debugPrint('Database connection test FAILED: $e');
    // Don't throw error - let app continue even if test fails
  }
}

/// Handle initialization errors gracefully
void _handleInitializationError(Object error, StackTrace stackTrace) {
  // Log the error
  debugPrint('Initialization Error: $error');
  debugPrint('Stack Trace: $stackTrace');

  // Run a minimal error app
  runApp(
    MaterialApp(
      title: AppConstants.adminAppName,
      home: Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Initialization Failed',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to initialize the admin panel.\n'
                  'Please check your configuration and try again.\n\n'
                  'Error: ${error.toString()}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () {
                    // Restart the app
                    main();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
  );
}
