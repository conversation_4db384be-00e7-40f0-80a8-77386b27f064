import 'package:logger/logger.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';

import 'models/analytics_event.dart';

/// [AdminAnalyticsService] handles analytics tracking for the admin panel.
///
/// This service provides methods for:
/// - Tracking user interactions and admin actions
/// - Setting user properties for analytics
/// - Managing analytics state and user identification
///
/// Uses Mixpanel for analytics tracking with error handling and logging.
class AdminAnalyticsService {
  final Mixpanel _mixpanel;
  final Logger _logger = Logger();

  /// Creates an [AdminAnalyticsService] instance.
  AdminAnalyticsService(this._mixpanel);

  /// Tracks an analytics event with optional properties.
  ///
  /// [event] - The analytics event to track
  ///
  /// Handles errors gracefully and logs them without throwing exceptions.
  Future<void> trackEvent(AnalyticsEvent event) async {
    try {
      final properties = Map<String, dynamic>.from(event.properties);

      // Add timestamp if not provided
      if (event.timestamp != null) {
        properties[AnalyticsProperties.timestamp] =
            event.timestamp!.toIso8601String();
      } else {
        properties[AnalyticsProperties.timestamp] =
            DateTime.now().toIso8601String();
      }

      await _mixpanel.track(event.name, properties: properties);

      _logger.d('Analytics event tracked: ${event.name}');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to track analytics event: ${event.name}',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - analytics failures shouldn't break the app
    }
  }

  /// Sets user properties for analytics.
  ///
  /// [properties] - Map of user properties to set
  ///
  /// Handles errors gracefully and logs them without throwing exceptions.
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    try {
      // Set each property individually as required by Mixpanel API
      for (final entry in properties.entries) {
        _mixpanel.getPeople().set(entry.key, entry.value);
      }

      _logger.d('User properties set: ${properties.keys.join(', ')}');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to set user properties',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - analytics failures shouldn't break the app
    }
  }

  /// Identifies a user with a distinct ID.
  ///
  /// [userId] - The unique identifier for the user
  ///
  /// Handles errors gracefully and logs them without throwing exceptions.
  Future<void> identifyUser(String userId) async {
    try {
      await _mixpanel.identify(userId);

      _logger.d('User identified: $userId');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to identify user: $userId',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - analytics failures shouldn't break the app
    }
  }

  /// Flushes pending analytics events.
  ///
  /// Forces immediate sending of queued events to the analytics service.
  /// Handles errors gracefully and logs them without throwing exceptions.
  Future<void> flush() async {
    try {
      await _mixpanel.flush();

      _logger.d('Analytics events flushed');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to flush analytics events',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - analytics failures shouldn't break the app
    }
  }

  /// Resets the analytics state.
  ///
  /// Clears user identification and resets analytics state.
  /// Handles errors gracefully and logs them without throwing exceptions.
  Future<void> reset() async {
    try {
      await _mixpanel.reset();

      _logger.d('Analytics state reset');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to reset analytics state',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - analytics failures shouldn't break the app
    }
  }

  /// Disposes the analytics service.
  ///
  /// Flushes any pending events before disposal.
  Future<void> dispose() async {
    try {
      await flush();
      _logger.d('Analytics service disposed');
    } catch (error, stackTrace) {
      _logger.e(
        'Error during analytics service disposal',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Tracks a predefined admin event with common properties.
  ///
  /// [eventName] - The predefined event name from [AdminAnalyticsEvents]
  /// [properties] - Additional properties for the event
  Future<void> trackAdminEvent(
    String eventName, {
    Map<String, dynamic>? properties,
  }) async {
    final event = AnalyticsEvent(
      name: eventName,
      properties: properties ?? {},
      timestamp: DateTime.now(),
    );

    await trackEvent(event);
  }

  /// Tracks user authentication events.
  ///
  /// [action] - The authentication action (login, logout, etc.)
  /// [userId] - The user ID
  /// [userRole] - The user role
  /// [success] - Whether the action was successful
  /// [additionalProperties] - Additional event properties
  Future<void> trackAuthEvent(
    String action, {
    required String userId,
    required String userRole,
    required bool success,
    Map<String, dynamic>? additionalProperties,
  }) async {
    final properties = <String, dynamic>{
      AnalyticsProperties.userId: userId,
      AnalyticsProperties.userRole: userRole,
      AnalyticsProperties.actionResult: success ? 'success' : 'failure',
      ...?additionalProperties,
    };

    await trackAdminEvent(action, properties: properties);
  }

  /// Tracks content management events.
  ///
  /// [action] - The content action (created, updated, deleted, etc.)
  /// [contentId] - The content ID
  /// [contentType] - The type of content
  /// [userId] - The user performing the action
  /// [additionalProperties] - Additional event properties
  Future<void> trackContentEvent(
    String action, {
    required String contentId,
    required String contentType,
    required String userId,
    Map<String, dynamic>? additionalProperties,
  }) async {
    final properties = <String, dynamic>{
      AnalyticsProperties.contentId: contentId,
      AnalyticsProperties.contentType: contentType,
      AnalyticsProperties.userId: userId,
      ...?additionalProperties,
    };

    await trackAdminEvent(action, properties: properties);
  }

  /// Tracks system administration events.
  ///
  /// [action] - The system action
  /// [userId] - The admin user performing the action
  /// [target] - The target of the action
  /// [additionalProperties] - Additional event properties
  Future<void> trackSystemEvent(
    String action, {
    required String userId,
    String? target,
    Map<String, dynamic>? additionalProperties,
  }) async {
    final properties = <String, dynamic>{
      AnalyticsProperties.userId: userId,
      if (target != null) AnalyticsProperties.actionTarget: target,
      ...?additionalProperties,
    };

    await trackAdminEvent(action, properties: properties);
  }

  /// Tracks performance-related events.
  ///
  /// [eventName] - The performance event name
  /// [duration] - The duration of the operation
  /// [additionalProperties] - Additional event properties
  Future<void> trackPerformanceEvent(
    String eventName, {
    required Duration duration,
    Map<String, dynamic>? additionalProperties,
  }) async {
    final properties = <String, dynamic>{
      AnalyticsProperties.actionDuration: duration.inMilliseconds,
      AnalyticsProperties.responseTime: duration.inMilliseconds,
      ...?additionalProperties,
    };

    await trackAdminEvent(eventName, properties: properties);
  }

  /// Tracks error events.
  ///
  /// [error] - The error that occurred
  /// [context] - Additional context about the error
  Future<void> trackErrorEvent(
    Object error, {
    Map<String, dynamic>? context,
  }) async {
    final properties = <String, dynamic>{
      AnalyticsProperties.errorType: error.runtimeType.toString(),
      AnalyticsProperties.errorMessage: error.toString(),
      if (context != null) AnalyticsProperties.errorContext: context,
    };

    await trackAdminEvent(AdminAnalyticsEvents.errorOccurred,
        properties: properties);
  }
}
