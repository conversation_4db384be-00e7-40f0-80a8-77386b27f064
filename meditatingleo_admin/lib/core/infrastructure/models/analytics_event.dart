import 'package:freezed_annotation/freezed_annotation.dart';

part 'analytics_event.freezed.dart';
part 'analytics_event.g.dart';

/// [AnalyticsEvent] represents an analytics event to be tracked.
///
/// Used for tracking user interactions, admin actions, and system events
/// in the admin panel for analytics and monitoring purposes.
@freezed
class AnalyticsEvent with _$AnalyticsEvent {
  /// Creates an [AnalyticsEvent] instance.
  ///
  /// [name] is the event name (required)
  /// [properties] are optional key-value pairs for additional context
  /// [timestamp] is automatically set to current time if not provided
  const factory AnalyticsEvent({
    required String name,
    @Default({}) Map<String, dynamic> properties,
    DateTime? timestamp,
  }) = _AnalyticsEvent;

  /// Creates an [AnalyticsEvent] from JSON.
  factory AnalyticsEvent.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsEventFromJson(json);
}

/// Predefined analytics events for the admin panel
class AdminAnalyticsEvents {
  // Authentication Events
  static const String adminLogin = 'admin_login';
  static const String adminLogout = 'admin_logout';
  static const String mfaVerification = 'mfa_verification';
  static const String passwordReset = 'password_reset';

  // Content Management Events
  static const String contentCreated = 'content_created';
  static const String contentUpdated = 'content_updated';
  static const String contentDeleted = 'content_deleted';
  static const String contentPublished = 'content_published';
  static const String contentUnpublished = 'content_unpublished';

  // User Management Events
  static const String userCreated = 'user_created';
  static const String userUpdated = 'user_updated';
  static const String userDeleted = 'user_deleted';
  static const String userBanned = 'user_banned';
  static const String userUnbanned = 'user_unbanned';

  // System Administration Events
  static const String systemConfigUpdated = 'system_config_updated';
  static const String backupCreated = 'backup_created';
  static const String backupRestored = 'backup_restored';
  static const String maintenanceModeEnabled = 'maintenance_mode_enabled';
  static const String maintenanceModeDisabled = 'maintenance_mode_disabled';

  // Navigation Events
  static const String pageViewed = 'page_viewed';
  static const String dashboardViewed = 'dashboard_viewed';
  static const String analyticsViewed = 'analytics_viewed';
  static const String monitoringViewed = 'monitoring_viewed';

  // Error Events
  static const String errorOccurred = 'error_occurred';
  static const String warningTriggered = 'warning_triggered';
  static const String criticalAlertTriggered = 'critical_alert_triggered';

  // Performance Events
  static const String slowQueryDetected = 'slow_query_detected';
  static const String highMemoryUsage = 'high_memory_usage';
  static const String highCpuUsage = 'high_cpu_usage';

  // Security Events
  static const String suspiciousActivityDetected = 'suspicious_activity_detected';
  static const String unauthorizedAccessAttempt = 'unauthorized_access_attempt';
  static const String securityPolicyViolation = 'security_policy_violation';

  // Audit Events
  static const String auditLogViewed = 'audit_log_viewed';
  static const String auditLogExported = 'audit_log_exported';
  static const String complianceReportGenerated = 'compliance_report_generated';

  // Feature Usage Events
  static const String bulkOperationPerformed = 'bulk_operation_performed';
  static const String dataExported = 'data_exported';
  static const String dataImported = 'data_imported';
  static const String reportGenerated = 'report_generated';
  static const String searchPerformed = 'search_performed';
  static const String filterApplied = 'filter_applied';

  // System Health Events
  static const String healthCheckPerformed = 'health_check_performed';
  static const String systemStatusChanged = 'system_status_changed';
  static const String serviceRestarted = 'service_restarted';
  static const String databaseConnectionLost = 'database_connection_lost';
  static const String databaseConnectionRestored = 'database_connection_restored';
}

/// Common property keys for analytics events
class AnalyticsProperties {
  // User Properties
  static const String userId = 'user_id';
  static const String userRole = 'user_role';
  static const String userEmail = 'user_email';
  static const String userDepartment = 'user_department';

  // Session Properties
  static const String sessionId = 'session_id';
  static const String sessionDuration = 'session_duration';
  static const String deviceType = 'device_type';
  static const String browserType = 'browser_type';
  static const String operatingSystem = 'operating_system';

  // Content Properties
  static const String contentId = 'content_id';
  static const String contentType = 'content_type';
  static const String contentCategory = 'content_category';
  static const String contentStatus = 'content_status';
  static const String contentSize = 'content_size';

  // Action Properties
  static const String actionType = 'action_type';
  static const String actionResult = 'action_result';
  static const String actionDuration = 'action_duration';
  static const String actionTarget = 'action_target';

  // Error Properties
  static const String errorType = 'error_type';
  static const String errorMessage = 'error_message';
  static const String errorCode = 'error_code';
  static const String errorSeverity = 'error_severity';
  static const String errorContext = 'error_context';

  // Performance Properties
  static const String responseTime = 'response_time';
  static const String memoryUsage = 'memory_usage';
  static const String cpuUsage = 'cpu_usage';
  static const String diskUsage = 'disk_usage';
  static const String networkLatency = 'network_latency';

  // System Properties
  static const String systemVersion = 'system_version';
  static const String appVersion = 'app_version';
  static const String environment = 'environment';
  static const String region = 'region';
  static const String timezone = 'timezone';

  // Timestamp Properties
  static const String timestamp = 'timestamp';
  static const String eventTime = 'event_time';
  static const String createdAt = 'created_at';
  static const String updatedAt = 'updated_at';

  // Feature Properties
  static const String featureName = 'feature_name';
  static const String featureVersion = 'feature_version';
  static const String featureFlag = 'feature_flag';
  static const String experimentId = 'experiment_id';
  static const String variantId = 'variant_id';

  // Business Properties
  static const String businessUnit = 'business_unit';
  static const String costCenter = 'cost_center';
  static const String project = 'project';
  static const String campaign = 'campaign';
  static const String source = 'source';
  static const String medium = 'medium';

  // Security Properties
  static const String ipAddress = 'ip_address';
  static const String userAgent = 'user_agent';
  static const String referrer = 'referrer';
  static const String securityLevel = 'security_level';
  static const String authMethod = 'auth_method';

  // Compliance Properties
  static const String complianceLevel = 'compliance_level';
  static const String dataClassification = 'data_classification';
  static const String retentionPeriod = 'retention_period';
  static const String privacyLevel = 'privacy_level';
  static const String consentStatus = 'consent_status';
}
