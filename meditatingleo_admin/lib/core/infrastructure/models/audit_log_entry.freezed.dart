// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'audit_log_entry.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AuditLogEntry _$AuditLogEntryFromJson(Map<String, dynamic> json) {
  return _AuditLogEntry.fromJson(json);
}

/// @nodoc
mixin _$AuditLogEntry {
  String get id => throw _privateConstructorUsedError;
  String get action => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String get userEmail => throw _privateConstructorUsedError;
  String get userRole => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  AuditLogLevel get level => throw _privateConstructorUsedError;
  AuditLogCategory get category => throw _privateConstructorUsedError;
  String? get resourceId => throw _privateConstructorUsedError;
  String? get resourceType => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;
  Map<String, dynamic> get beforeState => throw _privateConstructorUsedError;
  Map<String, dynamic> get afterState => throw _privateConstructorUsedError;
  String? get ipAddress => throw _privateConstructorUsedError;
  String? get userAgent => throw _privateConstructorUsedError;
  String? get sessionId => throw _privateConstructorUsedError;
  String? get requestId => throw _privateConstructorUsedError;
  bool? get success => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  Duration? get duration => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AuditLogEntry value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AuditLogEntry value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AuditLogEntry value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this AuditLogEntry to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AuditLogEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuditLogEntryCopyWith<AuditLogEntry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuditLogEntryCopyWith<$Res> {
  factory $AuditLogEntryCopyWith(
          AuditLogEntry value, $Res Function(AuditLogEntry) then) =
      _$AuditLogEntryCopyWithImpl<$Res, AuditLogEntry>;
  @useResult
  $Res call(
      {String id,
      String action,
      String userId,
      String userEmail,
      String userRole,
      DateTime timestamp,
      AuditLogLevel level,
      AuditLogCategory category,
      String? resourceId,
      String? resourceType,
      String? description,
      Map<String, dynamic> metadata,
      Map<String, dynamic> beforeState,
      Map<String, dynamic> afterState,
      String? ipAddress,
      String? userAgent,
      String? sessionId,
      String? requestId,
      bool? success,
      String? errorMessage,
      Duration? duration});
}

/// @nodoc
class _$AuditLogEntryCopyWithImpl<$Res, $Val extends AuditLogEntry>
    implements $AuditLogEntryCopyWith<$Res> {
  _$AuditLogEntryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuditLogEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? action = null,
    Object? userId = null,
    Object? userEmail = null,
    Object? userRole = null,
    Object? timestamp = null,
    Object? level = null,
    Object? category = null,
    Object? resourceId = freezed,
    Object? resourceType = freezed,
    Object? description = freezed,
    Object? metadata = null,
    Object? beforeState = null,
    Object? afterState = null,
    Object? ipAddress = freezed,
    Object? userAgent = freezed,
    Object? sessionId = freezed,
    Object? requestId = freezed,
    Object? success = freezed,
    Object? errorMessage = freezed,
    Object? duration = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userEmail: null == userEmail
          ? _value.userEmail
          : userEmail // ignore: cast_nullable_to_non_nullable
              as String,
      userRole: null == userRole
          ? _value.userRole
          : userRole // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as AuditLogLevel,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as AuditLogCategory,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceType: freezed == resourceType
          ? _value.resourceType
          : resourceType // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: null == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      beforeState: null == beforeState
          ? _value.beforeState
          : beforeState // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      afterState: null == afterState
          ? _value.afterState
          : afterState // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      userAgent: freezed == userAgent
          ? _value.userAgent
          : userAgent // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      requestId: freezed == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String?,
      success: freezed == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuditLogEntryImplCopyWith<$Res>
    implements $AuditLogEntryCopyWith<$Res> {
  factory _$$AuditLogEntryImplCopyWith(
          _$AuditLogEntryImpl value, $Res Function(_$AuditLogEntryImpl) then) =
      __$$AuditLogEntryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String action,
      String userId,
      String userEmail,
      String userRole,
      DateTime timestamp,
      AuditLogLevel level,
      AuditLogCategory category,
      String? resourceId,
      String? resourceType,
      String? description,
      Map<String, dynamic> metadata,
      Map<String, dynamic> beforeState,
      Map<String, dynamic> afterState,
      String? ipAddress,
      String? userAgent,
      String? sessionId,
      String? requestId,
      bool? success,
      String? errorMessage,
      Duration? duration});
}

/// @nodoc
class __$$AuditLogEntryImplCopyWithImpl<$Res>
    extends _$AuditLogEntryCopyWithImpl<$Res, _$AuditLogEntryImpl>
    implements _$$AuditLogEntryImplCopyWith<$Res> {
  __$$AuditLogEntryImplCopyWithImpl(
      _$AuditLogEntryImpl _value, $Res Function(_$AuditLogEntryImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuditLogEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? action = null,
    Object? userId = null,
    Object? userEmail = null,
    Object? userRole = null,
    Object? timestamp = null,
    Object? level = null,
    Object? category = null,
    Object? resourceId = freezed,
    Object? resourceType = freezed,
    Object? description = freezed,
    Object? metadata = null,
    Object? beforeState = null,
    Object? afterState = null,
    Object? ipAddress = freezed,
    Object? userAgent = freezed,
    Object? sessionId = freezed,
    Object? requestId = freezed,
    Object? success = freezed,
    Object? errorMessage = freezed,
    Object? duration = freezed,
  }) {
    return _then(_$AuditLogEntryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userEmail: null == userEmail
          ? _value.userEmail
          : userEmail // ignore: cast_nullable_to_non_nullable
              as String,
      userRole: null == userRole
          ? _value.userRole
          : userRole // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as AuditLogLevel,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as AuditLogCategory,
      resourceId: freezed == resourceId
          ? _value.resourceId
          : resourceId // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceType: freezed == resourceType
          ? _value.resourceType
          : resourceType // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: null == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      beforeState: null == beforeState
          ? _value._beforeState
          : beforeState // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      afterState: null == afterState
          ? _value._afterState
          : afterState // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      ipAddress: freezed == ipAddress
          ? _value.ipAddress
          : ipAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      userAgent: freezed == userAgent
          ? _value.userAgent
          : userAgent // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      requestId: freezed == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String?,
      success: freezed == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuditLogEntryImpl implements _AuditLogEntry {
  const _$AuditLogEntryImpl(
      {required this.id,
      required this.action,
      required this.userId,
      required this.userEmail,
      required this.userRole,
      required this.timestamp,
      required this.level,
      required this.category,
      this.resourceId,
      this.resourceType,
      this.description,
      final Map<String, dynamic> metadata = const {},
      final Map<String, dynamic> beforeState = const {},
      final Map<String, dynamic> afterState = const {},
      this.ipAddress,
      this.userAgent,
      this.sessionId,
      this.requestId,
      this.success,
      this.errorMessage,
      this.duration})
      : _metadata = metadata,
        _beforeState = beforeState,
        _afterState = afterState;

  factory _$AuditLogEntryImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuditLogEntryImplFromJson(json);

  @override
  final String id;
  @override
  final String action;
  @override
  final String userId;
  @override
  final String userEmail;
  @override
  final String userRole;
  @override
  final DateTime timestamp;
  @override
  final AuditLogLevel level;
  @override
  final AuditLogCategory category;
  @override
  final String? resourceId;
  @override
  final String? resourceType;
  @override
  final String? description;
  final Map<String, dynamic> _metadata;
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  final Map<String, dynamic> _beforeState;
  @override
  @JsonKey()
  Map<String, dynamic> get beforeState {
    if (_beforeState is EqualUnmodifiableMapView) return _beforeState;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_beforeState);
  }

  final Map<String, dynamic> _afterState;
  @override
  @JsonKey()
  Map<String, dynamic> get afterState {
    if (_afterState is EqualUnmodifiableMapView) return _afterState;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_afterState);
  }

  @override
  final String? ipAddress;
  @override
  final String? userAgent;
  @override
  final String? sessionId;
  @override
  final String? requestId;
  @override
  final bool? success;
  @override
  final String? errorMessage;
  @override
  final Duration? duration;

  @override
  String toString() {
    return 'AuditLogEntry(id: $id, action: $action, userId: $userId, userEmail: $userEmail, userRole: $userRole, timestamp: $timestamp, level: $level, category: $category, resourceId: $resourceId, resourceType: $resourceType, description: $description, metadata: $metadata, beforeState: $beforeState, afterState: $afterState, ipAddress: $ipAddress, userAgent: $userAgent, sessionId: $sessionId, requestId: $requestId, success: $success, errorMessage: $errorMessage, duration: $duration)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuditLogEntryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userEmail, userEmail) ||
                other.userEmail == userEmail) &&
            (identical(other.userRole, userRole) ||
                other.userRole == userRole) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.resourceId, resourceId) ||
                other.resourceId == resourceId) &&
            (identical(other.resourceType, resourceType) ||
                other.resourceType == resourceType) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata) &&
            const DeepCollectionEquality()
                .equals(other._beforeState, _beforeState) &&
            const DeepCollectionEquality()
                .equals(other._afterState, _afterState) &&
            (identical(other.ipAddress, ipAddress) ||
                other.ipAddress == ipAddress) &&
            (identical(other.userAgent, userAgent) ||
                other.userAgent == userAgent) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.duration, duration) ||
                other.duration == duration));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        action,
        userId,
        userEmail,
        userRole,
        timestamp,
        level,
        category,
        resourceId,
        resourceType,
        description,
        const DeepCollectionEquality().hash(_metadata),
        const DeepCollectionEquality().hash(_beforeState),
        const DeepCollectionEquality().hash(_afterState),
        ipAddress,
        userAgent,
        sessionId,
        requestId,
        success,
        errorMessage,
        duration
      ]);

  /// Create a copy of AuditLogEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuditLogEntryImplCopyWith<_$AuditLogEntryImpl> get copyWith =>
      __$$AuditLogEntryImplCopyWithImpl<_$AuditLogEntryImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AuditLogEntry value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AuditLogEntry value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AuditLogEntry value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$AuditLogEntryImplToJson(
      this,
    );
  }
}

abstract class _AuditLogEntry implements AuditLogEntry {
  const factory _AuditLogEntry(
      {required final String id,
      required final String action,
      required final String userId,
      required final String userEmail,
      required final String userRole,
      required final DateTime timestamp,
      required final AuditLogLevel level,
      required final AuditLogCategory category,
      final String? resourceId,
      final String? resourceType,
      final String? description,
      final Map<String, dynamic> metadata,
      final Map<String, dynamic> beforeState,
      final Map<String, dynamic> afterState,
      final String? ipAddress,
      final String? userAgent,
      final String? sessionId,
      final String? requestId,
      final bool? success,
      final String? errorMessage,
      final Duration? duration}) = _$AuditLogEntryImpl;

  factory _AuditLogEntry.fromJson(Map<String, dynamic> json) =
      _$AuditLogEntryImpl.fromJson;

  @override
  String get id;
  @override
  String get action;
  @override
  String get userId;
  @override
  String get userEmail;
  @override
  String get userRole;
  @override
  DateTime get timestamp;
  @override
  AuditLogLevel get level;
  @override
  AuditLogCategory get category;
  @override
  String? get resourceId;
  @override
  String? get resourceType;
  @override
  String? get description;
  @override
  Map<String, dynamic> get metadata;
  @override
  Map<String, dynamic> get beforeState;
  @override
  Map<String, dynamic> get afterState;
  @override
  String? get ipAddress;
  @override
  String? get userAgent;
  @override
  String? get sessionId;
  @override
  String? get requestId;
  @override
  bool? get success;
  @override
  String? get errorMessage;
  @override
  Duration? get duration;

  /// Create a copy of AuditLogEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuditLogEntryImplCopyWith<_$AuditLogEntryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
