import 'package:freezed_annotation/freezed_annotation.dart';

part 'audit_log_entry.freezed.dart';
part 'audit_log_entry.g.dart';

/// [AuditLogEntry] represents a single audit log entry for compliance and security tracking.
///
/// Used for tracking all administrative actions, security events, and system changes
/// in the admin panel for compliance, security, and accountability purposes.
@freezed
class AuditLogEntry with _$AuditLogEntry {
  /// Creates an [AuditLogEntry] instance.
  const factory AuditLogEntry({
    required String id,
    required String action,
    required String userId,
    required String userEmail,
    required String userRole,
    required DateTime timestamp,
    required AuditLogLevel level,
    required AuditLogCategory category,
    String? resourceId,
    String? resourceType,
    String? description,
    @Default({}) Map<String, dynamic> metadata,
    @Default({}) Map<String, dynamic> beforeState,
    @Default({}) Map<String, dynamic> afterState,
    String? ipAddress,
    String? userAgent,
    String? sessionId,
    String? requestId,
    bool? success,
    String? errorMessage,
    Duration? duration,
  }) = _AuditLogEntry;

  /// Creates an [AuditLogEntry] from JSON.
  factory AuditLogEntry.fromJson(Map<String, dynamic> json) =>
      _$AuditLogEntryFromJson(json);
}

/// Extension methods for [AuditLogEntry]
extension AuditLogEntryX on AuditLogEntry {
  /// Returns a human-readable summary of the audit log entry
  String get summary {
    final actionDesc = description ?? action;
    final userDesc = '$userEmail ($userRole)';
    final resourceDesc = resourceType != null && resourceId != null
        ? ' on $resourceType:$resourceId'
        : '';
    
    return '$userDesc performed $actionDesc$resourceDesc';
  }

  /// Returns the severity color for the log level
  String get levelColor {
    switch (level) {
      case AuditLogLevel.info:
        return '#2196F3'; // Blue
      case AuditLogLevel.warning:
        return '#FF9800'; // Orange
      case AuditLogLevel.error:
        return '#F44336'; // Red
      case AuditLogLevel.critical:
        return '#9C27B0'; // Purple
      case AuditLogLevel.security:
        return '#FF5722'; // Deep Orange
    }
  }

  /// Returns the icon for the log level
  String get levelIcon {
    switch (level) {
      case AuditLogLevel.info:
        return 'ℹ️';
      case AuditLogLevel.warning:
        return '⚠️';
      case AuditLogLevel.error:
        return '❌';
      case AuditLogLevel.critical:
        return '🚨';
      case AuditLogLevel.security:
        return '🔒';
    }
  }

  /// Returns the category icon
  String get categoryIcon {
    switch (category) {
      case AuditLogCategory.authentication:
        return '🔐';
      case AuditLogCategory.authorization:
        return '🛡️';
      case AuditLogCategory.contentManagement:
        return '📝';
      case AuditLogCategory.userManagement:
        return '👥';
      case AuditLogCategory.systemAdministration:
        return '⚙️';
      case AuditLogCategory.dataAccess:
        return '📊';
      case AuditLogCategory.configuration:
        return '🔧';
      case AuditLogCategory.security:
        return '🔒';
      case AuditLogCategory.compliance:
        return '📋';
      case AuditLogCategory.performance:
        return '📈';
      case AuditLogCategory.error:
        return '🐛';
      case AuditLogCategory.system:
        return '💻';
    }
  }

  /// Returns true if this entry represents a sensitive action
  bool get isSensitive {
    return level == AuditLogLevel.security ||
           level == AuditLogLevel.critical ||
           category == AuditLogCategory.security ||
           category == AuditLogCategory.authentication ||
           category == AuditLogCategory.authorization;
  }

  /// Returns true if this entry represents a failed action
  bool get isFailure => success == false;

  /// Returns true if this entry has state changes
  bool get hasStateChanges => beforeState.isNotEmpty || afterState.isNotEmpty;
}

/// Enum representing different audit log levels
enum AuditLogLevel {
  @JsonValue('info')
  info,
  @JsonValue('warning')
  warning,
  @JsonValue('error')
  error,
  @JsonValue('critical')
  critical,
  @JsonValue('security')
  security,
}

/// Extension methods for [AuditLogLevel]
extension AuditLogLevelX on AuditLogLevel {
  /// Returns the display name for the level
  String get displayName {
    switch (this) {
      case AuditLogLevel.info:
        return 'Info';
      case AuditLogLevel.warning:
        return 'Warning';
      case AuditLogLevel.error:
        return 'Error';
      case AuditLogLevel.critical:
        return 'Critical';
      case AuditLogLevel.security:
        return 'Security';
    }
  }

  /// Returns the priority level (higher number = higher priority)
  int get priority {
    switch (this) {
      case AuditLogLevel.critical:
        return 5;
      case AuditLogLevel.security:
        return 4;
      case AuditLogLevel.error:
        return 3;
      case AuditLogLevel.warning:
        return 2;
      case AuditLogLevel.info:
        return 1;
    }
  }
}

/// Enum representing different audit log categories
enum AuditLogCategory {
  @JsonValue('authentication')
  authentication,
  @JsonValue('authorization')
  authorization,
  @JsonValue('content_management')
  contentManagement,
  @JsonValue('user_management')
  userManagement,
  @JsonValue('system_administration')
  systemAdministration,
  @JsonValue('data_access')
  dataAccess,
  @JsonValue('configuration')
  configuration,
  @JsonValue('security')
  security,
  @JsonValue('compliance')
  compliance,
  @JsonValue('performance')
  performance,
  @JsonValue('error')
  error,
  @JsonValue('system')
  system,
}

/// Extension methods for [AuditLogCategory]
extension AuditLogCategoryX on AuditLogCategory {
  /// Returns the display name for the category
  String get displayName {
    switch (this) {
      case AuditLogCategory.authentication:
        return 'Authentication';
      case AuditLogCategory.authorization:
        return 'Authorization';
      case AuditLogCategory.contentManagement:
        return 'Content Management';
      case AuditLogCategory.userManagement:
        return 'User Management';
      case AuditLogCategory.systemAdministration:
        return 'System Administration';
      case AuditLogCategory.dataAccess:
        return 'Data Access';
      case AuditLogCategory.configuration:
        return 'Configuration';
      case AuditLogCategory.security:
        return 'Security';
      case AuditLogCategory.compliance:
        return 'Compliance';
      case AuditLogCategory.performance:
        return 'Performance';
      case AuditLogCategory.error:
        return 'Error';
      case AuditLogCategory.system:
        return 'System';
    }
  }
}

/// Predefined audit actions for the admin panel
class AuditActions {
  // Authentication Actions
  static const String login = 'login';
  static const String logout = 'logout';
  static const String loginFailed = 'login_failed';
  static const String passwordChanged = 'password_changed';
  static const String mfaEnabled = 'mfa_enabled';
  static const String mfaDisabled = 'mfa_disabled';
  static const String mfaVerified = 'mfa_verified';
  static const String mfaFailed = 'mfa_failed';

  // Authorization Actions
  static const String accessGranted = 'access_granted';
  static const String accessDenied = 'access_denied';
  static const String roleAssigned = 'role_assigned';
  static const String roleRevoked = 'role_revoked';
  static const String permissionGranted = 'permission_granted';
  static const String permissionRevoked = 'permission_revoked';

  // Content Management Actions
  static const String contentCreated = 'content_created';
  static const String contentUpdated = 'content_updated';
  static const String contentDeleted = 'content_deleted';
  static const String contentPublished = 'content_published';
  static const String contentUnpublished = 'content_unpublished';
  static const String contentViewed = 'content_viewed';
  static const String contentExported = 'content_exported';
  static const String contentImported = 'content_imported';

  // User Management Actions
  static const String userCreated = 'user_created';
  static const String userUpdated = 'user_updated';
  static const String userDeleted = 'user_deleted';
  static const String userActivated = 'user_activated';
  static const String userDeactivated = 'user_deactivated';
  static const String userBanned = 'user_banned';
  static const String userUnbanned = 'user_unbanned';
  static const String userViewed = 'user_viewed';

  // System Administration Actions
  static const String configurationUpdated = 'configuration_updated';
  static const String systemRestarted = 'system_restarted';
  static const String maintenanceModeEnabled = 'maintenance_mode_enabled';
  static const String maintenanceModeDisabled = 'maintenance_mode_disabled';
  static const String backupCreated = 'backup_created';
  static const String backupRestored = 'backup_restored';
  static const String logViewed = 'log_viewed';
  static const String logExported = 'log_exported';

  // Data Access Actions
  static const String dataQueried = 'data_queried';
  static const String dataExported = 'data_exported';
  static const String dataImported = 'data_imported';
  static const String reportGenerated = 'report_generated';
  static const String analyticsViewed = 'analytics_viewed';

  // Security Actions
  static const String securityPolicyUpdated = 'security_policy_updated';
  static const String suspiciousActivityDetected = 'suspicious_activity_detected';
  static const String securityIncidentCreated = 'security_incident_created';
  static const String securityIncidentResolved = 'security_incident_resolved';
  static const String encryptionKeyRotated = 'encryption_key_rotated';

  // Error Actions
  static const String errorOccurred = 'error_occurred';
  static const String exceptionThrown = 'exception_thrown';
  static const String systemFailure = 'system_failure';
  static const String dataCorruption = 'data_corruption';
  static const String serviceUnavailable = 'service_unavailable';
}
