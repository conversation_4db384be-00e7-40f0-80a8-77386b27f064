// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'system_health_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SystemHealthStatus _$SystemHealthStatusFromJson(Map<String, dynamic> json) {
  return _SystemHealthStatus.fromJson(json);
}

/// @nodoc
mixin _$SystemHealthStatus {
  bool get networkConnected => throw _privateConstructorUsedError;
  bool get databaseHealthy => throw _privateConstructorUsedError;
  bool get authenticationHealthy => throw _privateConstructorUsedError;
  bool get storageHealthy => throw _privateConstructorUsedError;
  bool get apiHealthy => throw _privateConstructorUsedError;
  HealthStatus get overallStatus => throw _privateConstructorUsedError;
  DateTime get lastChecked => throw _privateConstructorUsedError;
  Map<String, dynamic> get additionalMetrics =>
      throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SystemHealthStatus value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SystemHealthStatus value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SystemHealthStatus value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this SystemHealthStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SystemHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SystemHealthStatusCopyWith<SystemHealthStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SystemHealthStatusCopyWith<$Res> {
  factory $SystemHealthStatusCopyWith(
          SystemHealthStatus value, $Res Function(SystemHealthStatus) then) =
      _$SystemHealthStatusCopyWithImpl<$Res, SystemHealthStatus>;
  @useResult
  $Res call(
      {bool networkConnected,
      bool databaseHealthy,
      bool authenticationHealthy,
      bool storageHealthy,
      bool apiHealthy,
      HealthStatus overallStatus,
      DateTime lastChecked,
      Map<String, dynamic> additionalMetrics,
      String? errorMessage});
}

/// @nodoc
class _$SystemHealthStatusCopyWithImpl<$Res, $Val extends SystemHealthStatus>
    implements $SystemHealthStatusCopyWith<$Res> {
  _$SystemHealthStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SystemHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? networkConnected = null,
    Object? databaseHealthy = null,
    Object? authenticationHealthy = null,
    Object? storageHealthy = null,
    Object? apiHealthy = null,
    Object? overallStatus = null,
    Object? lastChecked = null,
    Object? additionalMetrics = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      networkConnected: null == networkConnected
          ? _value.networkConnected
          : networkConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      databaseHealthy: null == databaseHealthy
          ? _value.databaseHealthy
          : databaseHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      authenticationHealthy: null == authenticationHealthy
          ? _value.authenticationHealthy
          : authenticationHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      storageHealthy: null == storageHealthy
          ? _value.storageHealthy
          : storageHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      apiHealthy: null == apiHealthy
          ? _value.apiHealthy
          : apiHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      overallStatus: null == overallStatus
          ? _value.overallStatus
          : overallStatus // ignore: cast_nullable_to_non_nullable
              as HealthStatus,
      lastChecked: null == lastChecked
          ? _value.lastChecked
          : lastChecked // ignore: cast_nullable_to_non_nullable
              as DateTime,
      additionalMetrics: null == additionalMetrics
          ? _value.additionalMetrics
          : additionalMetrics // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SystemHealthStatusImplCopyWith<$Res>
    implements $SystemHealthStatusCopyWith<$Res> {
  factory _$$SystemHealthStatusImplCopyWith(_$SystemHealthStatusImpl value,
          $Res Function(_$SystemHealthStatusImpl) then) =
      __$$SystemHealthStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool networkConnected,
      bool databaseHealthy,
      bool authenticationHealthy,
      bool storageHealthy,
      bool apiHealthy,
      HealthStatus overallStatus,
      DateTime lastChecked,
      Map<String, dynamic> additionalMetrics,
      String? errorMessage});
}

/// @nodoc
class __$$SystemHealthStatusImplCopyWithImpl<$Res>
    extends _$SystemHealthStatusCopyWithImpl<$Res, _$SystemHealthStatusImpl>
    implements _$$SystemHealthStatusImplCopyWith<$Res> {
  __$$SystemHealthStatusImplCopyWithImpl(_$SystemHealthStatusImpl _value,
      $Res Function(_$SystemHealthStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of SystemHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? networkConnected = null,
    Object? databaseHealthy = null,
    Object? authenticationHealthy = null,
    Object? storageHealthy = null,
    Object? apiHealthy = null,
    Object? overallStatus = null,
    Object? lastChecked = null,
    Object? additionalMetrics = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$SystemHealthStatusImpl(
      networkConnected: null == networkConnected
          ? _value.networkConnected
          : networkConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      databaseHealthy: null == databaseHealthy
          ? _value.databaseHealthy
          : databaseHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      authenticationHealthy: null == authenticationHealthy
          ? _value.authenticationHealthy
          : authenticationHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      storageHealthy: null == storageHealthy
          ? _value.storageHealthy
          : storageHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      apiHealthy: null == apiHealthy
          ? _value.apiHealthy
          : apiHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      overallStatus: null == overallStatus
          ? _value.overallStatus
          : overallStatus // ignore: cast_nullable_to_non_nullable
              as HealthStatus,
      lastChecked: null == lastChecked
          ? _value.lastChecked
          : lastChecked // ignore: cast_nullable_to_non_nullable
              as DateTime,
      additionalMetrics: null == additionalMetrics
          ? _value._additionalMetrics
          : additionalMetrics // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SystemHealthStatusImpl implements _SystemHealthStatus {
  const _$SystemHealthStatusImpl(
      {required this.networkConnected,
      required this.databaseHealthy,
      required this.authenticationHealthy,
      required this.storageHealthy,
      required this.apiHealthy,
      required this.overallStatus,
      required this.lastChecked,
      final Map<String, dynamic> additionalMetrics = const {},
      this.errorMessage})
      : _additionalMetrics = additionalMetrics;

  factory _$SystemHealthStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$SystemHealthStatusImplFromJson(json);

  @override
  final bool networkConnected;
  @override
  final bool databaseHealthy;
  @override
  final bool authenticationHealthy;
  @override
  final bool storageHealthy;
  @override
  final bool apiHealthy;
  @override
  final HealthStatus overallStatus;
  @override
  final DateTime lastChecked;
  final Map<String, dynamic> _additionalMetrics;
  @override
  @JsonKey()
  Map<String, dynamic> get additionalMetrics {
    if (_additionalMetrics is EqualUnmodifiableMapView)
      return _additionalMetrics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_additionalMetrics);
  }

  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'SystemHealthStatus(networkConnected: $networkConnected, databaseHealthy: $databaseHealthy, authenticationHealthy: $authenticationHealthy, storageHealthy: $storageHealthy, apiHealthy: $apiHealthy, overallStatus: $overallStatus, lastChecked: $lastChecked, additionalMetrics: $additionalMetrics, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SystemHealthStatusImpl &&
            (identical(other.networkConnected, networkConnected) ||
                other.networkConnected == networkConnected) &&
            (identical(other.databaseHealthy, databaseHealthy) ||
                other.databaseHealthy == databaseHealthy) &&
            (identical(other.authenticationHealthy, authenticationHealthy) ||
                other.authenticationHealthy == authenticationHealthy) &&
            (identical(other.storageHealthy, storageHealthy) ||
                other.storageHealthy == storageHealthy) &&
            (identical(other.apiHealthy, apiHealthy) ||
                other.apiHealthy == apiHealthy) &&
            (identical(other.overallStatus, overallStatus) ||
                other.overallStatus == overallStatus) &&
            (identical(other.lastChecked, lastChecked) ||
                other.lastChecked == lastChecked) &&
            const DeepCollectionEquality()
                .equals(other._additionalMetrics, _additionalMetrics) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      networkConnected,
      databaseHealthy,
      authenticationHealthy,
      storageHealthy,
      apiHealthy,
      overallStatus,
      lastChecked,
      const DeepCollectionEquality().hash(_additionalMetrics),
      errorMessage);

  /// Create a copy of SystemHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SystemHealthStatusImplCopyWith<_$SystemHealthStatusImpl> get copyWith =>
      __$$SystemHealthStatusImplCopyWithImpl<_$SystemHealthStatusImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SystemHealthStatus value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SystemHealthStatus value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SystemHealthStatus value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$SystemHealthStatusImplToJson(
      this,
    );
  }
}

abstract class _SystemHealthStatus implements SystemHealthStatus {
  const factory _SystemHealthStatus(
      {required final bool networkConnected,
      required final bool databaseHealthy,
      required final bool authenticationHealthy,
      required final bool storageHealthy,
      required final bool apiHealthy,
      required final HealthStatus overallStatus,
      required final DateTime lastChecked,
      final Map<String, dynamic> additionalMetrics,
      final String? errorMessage}) = _$SystemHealthStatusImpl;

  factory _SystemHealthStatus.fromJson(Map<String, dynamic> json) =
      _$SystemHealthStatusImpl.fromJson;

  @override
  bool get networkConnected;
  @override
  bool get databaseHealthy;
  @override
  bool get authenticationHealthy;
  @override
  bool get storageHealthy;
  @override
  bool get apiHealthy;
  @override
  HealthStatus get overallStatus;
  @override
  DateTime get lastChecked;
  @override
  Map<String, dynamic> get additionalMetrics;
  @override
  String? get errorMessage;

  /// Create a copy of SystemHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SystemHealthStatusImplCopyWith<_$SystemHealthStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ComponentHealthStatus _$ComponentHealthStatusFromJson(
    Map<String, dynamic> json) {
  return _ComponentHealthStatus.fromJson(json);
}

/// @nodoc
mixin _$ComponentHealthStatus {
  String get name => throw _privateConstructorUsedError;
  HealthStatus get status => throw _privateConstructorUsedError;
  DateTime get lastChecked => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, dynamic> get metrics => throw _privateConstructorUsedError;
  Duration? get responseTime => throw _privateConstructorUsedError;
  double? get uptime => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ComponentHealthStatus value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ComponentHealthStatus value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ComponentHealthStatus value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this ComponentHealthStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComponentHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComponentHealthStatusCopyWith<ComponentHealthStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComponentHealthStatusCopyWith<$Res> {
  factory $ComponentHealthStatusCopyWith(ComponentHealthStatus value,
          $Res Function(ComponentHealthStatus) then) =
      _$ComponentHealthStatusCopyWithImpl<$Res, ComponentHealthStatus>;
  @useResult
  $Res call(
      {String name,
      HealthStatus status,
      DateTime lastChecked,
      String? errorMessage,
      Map<String, dynamic> metrics,
      Duration? responseTime,
      double? uptime});
}

/// @nodoc
class _$ComponentHealthStatusCopyWithImpl<$Res,
        $Val extends ComponentHealthStatus>
    implements $ComponentHealthStatusCopyWith<$Res> {
  _$ComponentHealthStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComponentHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? status = null,
    Object? lastChecked = null,
    Object? errorMessage = freezed,
    Object? metrics = null,
    Object? responseTime = freezed,
    Object? uptime = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as HealthStatus,
      lastChecked: null == lastChecked
          ? _value.lastChecked
          : lastChecked // ignore: cast_nullable_to_non_nullable
              as DateTime,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      metrics: null == metrics
          ? _value.metrics
          : metrics // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      responseTime: freezed == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as Duration?,
      uptime: freezed == uptime
          ? _value.uptime
          : uptime // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ComponentHealthStatusImplCopyWith<$Res>
    implements $ComponentHealthStatusCopyWith<$Res> {
  factory _$$ComponentHealthStatusImplCopyWith(
          _$ComponentHealthStatusImpl value,
          $Res Function(_$ComponentHealthStatusImpl) then) =
      __$$ComponentHealthStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      HealthStatus status,
      DateTime lastChecked,
      String? errorMessage,
      Map<String, dynamic> metrics,
      Duration? responseTime,
      double? uptime});
}

/// @nodoc
class __$$ComponentHealthStatusImplCopyWithImpl<$Res>
    extends _$ComponentHealthStatusCopyWithImpl<$Res,
        _$ComponentHealthStatusImpl>
    implements _$$ComponentHealthStatusImplCopyWith<$Res> {
  __$$ComponentHealthStatusImplCopyWithImpl(_$ComponentHealthStatusImpl _value,
      $Res Function(_$ComponentHealthStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of ComponentHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? status = null,
    Object? lastChecked = null,
    Object? errorMessage = freezed,
    Object? metrics = null,
    Object? responseTime = freezed,
    Object? uptime = freezed,
  }) {
    return _then(_$ComponentHealthStatusImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as HealthStatus,
      lastChecked: null == lastChecked
          ? _value.lastChecked
          : lastChecked // ignore: cast_nullable_to_non_nullable
              as DateTime,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      metrics: null == metrics
          ? _value._metrics
          : metrics // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      responseTime: freezed == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as Duration?,
      uptime: freezed == uptime
          ? _value.uptime
          : uptime // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ComponentHealthStatusImpl implements _ComponentHealthStatus {
  const _$ComponentHealthStatusImpl(
      {required this.name,
      required this.status,
      required this.lastChecked,
      this.errorMessage,
      final Map<String, dynamic> metrics = const {},
      this.responseTime,
      this.uptime})
      : _metrics = metrics;

  factory _$ComponentHealthStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComponentHealthStatusImplFromJson(json);

  @override
  final String name;
  @override
  final HealthStatus status;
  @override
  final DateTime lastChecked;
  @override
  final String? errorMessage;
  final Map<String, dynamic> _metrics;
  @override
  @JsonKey()
  Map<String, dynamic> get metrics {
    if (_metrics is EqualUnmodifiableMapView) return _metrics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metrics);
  }

  @override
  final Duration? responseTime;
  @override
  final double? uptime;

  @override
  String toString() {
    return 'ComponentHealthStatus(name: $name, status: $status, lastChecked: $lastChecked, errorMessage: $errorMessage, metrics: $metrics, responseTime: $responseTime, uptime: $uptime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComponentHealthStatusImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.lastChecked, lastChecked) ||
                other.lastChecked == lastChecked) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(other._metrics, _metrics) &&
            (identical(other.responseTime, responseTime) ||
                other.responseTime == responseTime) &&
            (identical(other.uptime, uptime) || other.uptime == uptime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      status,
      lastChecked,
      errorMessage,
      const DeepCollectionEquality().hash(_metrics),
      responseTime,
      uptime);

  /// Create a copy of ComponentHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComponentHealthStatusImplCopyWith<_$ComponentHealthStatusImpl>
      get copyWith => __$$ComponentHealthStatusImplCopyWithImpl<
          _$ComponentHealthStatusImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ComponentHealthStatus value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ComponentHealthStatus value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ComponentHealthStatus value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ComponentHealthStatusImplToJson(
      this,
    );
  }
}

abstract class _ComponentHealthStatus implements ComponentHealthStatus {
  const factory _ComponentHealthStatus(
      {required final String name,
      required final HealthStatus status,
      required final DateTime lastChecked,
      final String? errorMessage,
      final Map<String, dynamic> metrics,
      final Duration? responseTime,
      final double? uptime}) = _$ComponentHealthStatusImpl;

  factory _ComponentHealthStatus.fromJson(Map<String, dynamic> json) =
      _$ComponentHealthStatusImpl.fromJson;

  @override
  String get name;
  @override
  HealthStatus get status;
  @override
  DateTime get lastChecked;
  @override
  String? get errorMessage;
  @override
  Map<String, dynamic> get metrics;
  @override
  Duration? get responseTime;
  @override
  double? get uptime;

  /// Create a copy of ComponentHealthStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComponentHealthStatusImplCopyWith<_$ComponentHealthStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}

HealthCheckResult _$HealthCheckResultFromJson(Map<String, dynamic> json) {
  return _HealthCheckResult.fromJson(json);
}

/// @nodoc
mixin _$HealthCheckResult {
  String get componentName => throw _privateConstructorUsedError;
  bool get isHealthy => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  Duration? get responseTime => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  Map<String, dynamic> get details => throw _privateConstructorUsedError;

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_HealthCheckResult value) $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_HealthCheckResult value)? $default,
  ) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_HealthCheckResult value)? $default, {
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this HealthCheckResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HealthCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HealthCheckResultCopyWith<HealthCheckResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HealthCheckResultCopyWith<$Res> {
  factory $HealthCheckResultCopyWith(
          HealthCheckResult value, $Res Function(HealthCheckResult) then) =
      _$HealthCheckResultCopyWithImpl<$Res, HealthCheckResult>;
  @useResult
  $Res call(
      {String componentName,
      bool isHealthy,
      DateTime timestamp,
      Duration? responseTime,
      String? errorMessage,
      Map<String, dynamic> details});
}

/// @nodoc
class _$HealthCheckResultCopyWithImpl<$Res, $Val extends HealthCheckResult>
    implements $HealthCheckResultCopyWith<$Res> {
  _$HealthCheckResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HealthCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? componentName = null,
    Object? isHealthy = null,
    Object? timestamp = null,
    Object? responseTime = freezed,
    Object? errorMessage = freezed,
    Object? details = null,
  }) {
    return _then(_value.copyWith(
      componentName: null == componentName
          ? _value.componentName
          : componentName // ignore: cast_nullable_to_non_nullable
              as String,
      isHealthy: null == isHealthy
          ? _value.isHealthy
          : isHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      responseTime: freezed == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as Duration?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      details: null == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HealthCheckResultImplCopyWith<$Res>
    implements $HealthCheckResultCopyWith<$Res> {
  factory _$$HealthCheckResultImplCopyWith(_$HealthCheckResultImpl value,
          $Res Function(_$HealthCheckResultImpl) then) =
      __$$HealthCheckResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String componentName,
      bool isHealthy,
      DateTime timestamp,
      Duration? responseTime,
      String? errorMessage,
      Map<String, dynamic> details});
}

/// @nodoc
class __$$HealthCheckResultImplCopyWithImpl<$Res>
    extends _$HealthCheckResultCopyWithImpl<$Res, _$HealthCheckResultImpl>
    implements _$$HealthCheckResultImplCopyWith<$Res> {
  __$$HealthCheckResultImplCopyWithImpl(_$HealthCheckResultImpl _value,
      $Res Function(_$HealthCheckResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of HealthCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? componentName = null,
    Object? isHealthy = null,
    Object? timestamp = null,
    Object? responseTime = freezed,
    Object? errorMessage = freezed,
    Object? details = null,
  }) {
    return _then(_$HealthCheckResultImpl(
      componentName: null == componentName
          ? _value.componentName
          : componentName // ignore: cast_nullable_to_non_nullable
              as String,
      isHealthy: null == isHealthy
          ? _value.isHealthy
          : isHealthy // ignore: cast_nullable_to_non_nullable
              as bool,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      responseTime: freezed == responseTime
          ? _value.responseTime
          : responseTime // ignore: cast_nullable_to_non_nullable
              as Duration?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      details: null == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HealthCheckResultImpl implements _HealthCheckResult {
  const _$HealthCheckResultImpl(
      {required this.componentName,
      required this.isHealthy,
      required this.timestamp,
      this.responseTime,
      this.errorMessage,
      final Map<String, dynamic> details = const {}})
      : _details = details;

  factory _$HealthCheckResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$HealthCheckResultImplFromJson(json);

  @override
  final String componentName;
  @override
  final bool isHealthy;
  @override
  final DateTime timestamp;
  @override
  final Duration? responseTime;
  @override
  final String? errorMessage;
  final Map<String, dynamic> _details;
  @override
  @JsonKey()
  Map<String, dynamic> get details {
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_details);
  }

  @override
  String toString() {
    return 'HealthCheckResult(componentName: $componentName, isHealthy: $isHealthy, timestamp: $timestamp, responseTime: $responseTime, errorMessage: $errorMessage, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HealthCheckResultImpl &&
            (identical(other.componentName, componentName) ||
                other.componentName == componentName) &&
            (identical(other.isHealthy, isHealthy) ||
                other.isHealthy == isHealthy) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.responseTime, responseTime) ||
                other.responseTime == responseTime) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      componentName,
      isHealthy,
      timestamp,
      responseTime,
      errorMessage,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of HealthCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HealthCheckResultImplCopyWith<_$HealthCheckResultImpl> get copyWith =>
      __$$HealthCheckResultImplCopyWithImpl<_$HealthCheckResultImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_HealthCheckResult value) $default,
  ) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_HealthCheckResult value)? $default,
  ) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_HealthCheckResult value)? $default, {
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$HealthCheckResultImplToJson(
      this,
    );
  }
}

abstract class _HealthCheckResult implements HealthCheckResult {
  const factory _HealthCheckResult(
      {required final String componentName,
      required final bool isHealthy,
      required final DateTime timestamp,
      final Duration? responseTime,
      final String? errorMessage,
      final Map<String, dynamic> details}) = _$HealthCheckResultImpl;

  factory _HealthCheckResult.fromJson(Map<String, dynamic> json) =
      _$HealthCheckResultImpl.fromJson;

  @override
  String get componentName;
  @override
  bool get isHealthy;
  @override
  DateTime get timestamp;
  @override
  Duration? get responseTime;
  @override
  String? get errorMessage;
  @override
  Map<String, dynamic> get details;

  /// Create a copy of HealthCheckResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HealthCheckResultImplCopyWith<_$HealthCheckResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
