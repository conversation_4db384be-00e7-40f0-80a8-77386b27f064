// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audit_log_entry.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuditLogEntryImpl _$$AuditLogEntryImplFromJson(Map<String, dynamic> json) =>
    _$AuditLogEntryImpl(
      id: json['id'] as String,
      action: json['action'] as String,
      userId: json['userId'] as String,
      userEmail: json['userEmail'] as String,
      userRole: json['userRole'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      level: $enumDecode(_$AuditLogLevelEnumMap, json['level']),
      category: $enumDecode(_$AuditLogCategoryEnumMap, json['category']),
      resourceId: json['resourceId'] as String?,
      resourceType: json['resourceType'] as String?,
      description: json['description'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
      beforeState: json['beforeState'] as Map<String, dynamic>? ?? const {},
      afterState: json['afterState'] as Map<String, dynamic>? ?? const {},
      ipAddress: json['ipAddress'] as String?,
      userAgent: json['userAgent'] as String?,
      sessionId: json['sessionId'] as String?,
      requestId: json['requestId'] as String?,
      success: json['success'] as bool?,
      errorMessage: json['errorMessage'] as String?,
      duration: json['duration'] == null
          ? null
          : Duration(microseconds: (json['duration'] as num).toInt()),
    );

Map<String, dynamic> _$$AuditLogEntryImplToJson(_$AuditLogEntryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'action': instance.action,
      'userId': instance.userId,
      'userEmail': instance.userEmail,
      'userRole': instance.userRole,
      'timestamp': instance.timestamp.toIso8601String(),
      'level': _$AuditLogLevelEnumMap[instance.level]!,
      'category': _$AuditLogCategoryEnumMap[instance.category]!,
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'description': instance.description,
      'metadata': instance.metadata,
      'beforeState': instance.beforeState,
      'afterState': instance.afterState,
      'ipAddress': instance.ipAddress,
      'userAgent': instance.userAgent,
      'sessionId': instance.sessionId,
      'requestId': instance.requestId,
      'success': instance.success,
      'errorMessage': instance.errorMessage,
      'duration': instance.duration?.inMicroseconds,
    };

const _$AuditLogLevelEnumMap = {
  AuditLogLevel.info: 'info',
  AuditLogLevel.warning: 'warning',
  AuditLogLevel.error: 'error',
  AuditLogLevel.critical: 'critical',
  AuditLogLevel.security: 'security',
};

const _$AuditLogCategoryEnumMap = {
  AuditLogCategory.authentication: 'authentication',
  AuditLogCategory.authorization: 'authorization',
  AuditLogCategory.contentManagement: 'content_management',
  AuditLogCategory.userManagement: 'user_management',
  AuditLogCategory.systemAdministration: 'system_administration',
  AuditLogCategory.dataAccess: 'data_access',
  AuditLogCategory.configuration: 'configuration',
  AuditLogCategory.security: 'security',
  AuditLogCategory.compliance: 'compliance',
  AuditLogCategory.performance: 'performance',
  AuditLogCategory.error: 'error',
  AuditLogCategory.system: 'system',
};
