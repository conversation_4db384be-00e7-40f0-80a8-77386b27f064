// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'system_health_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SystemHealthStatusImpl _$$SystemHealthStatusImplFromJson(
        Map<String, dynamic> json) =>
    _$SystemHealthStatusImpl(
      networkConnected: json['networkConnected'] as bool,
      databaseHealthy: json['databaseHealthy'] as bool,
      authenticationHealthy: json['authenticationHealthy'] as bool,
      storageHealthy: json['storageHealthy'] as bool,
      apiHealthy: json['apiHealthy'] as bool,
      overallStatus: $enumDecode(_$HealthStatusEnumMap, json['overallStatus']),
      lastChecked: DateTime.parse(json['lastChecked'] as String),
      additionalMetrics:
          json['additionalMetrics'] as Map<String, dynamic>? ?? const {},
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$$SystemHealthStatusImplToJson(
        _$SystemHealthStatusImpl instance) =>
    <String, dynamic>{
      'networkConnected': instance.networkConnected,
      'databaseHealthy': instance.databaseHealthy,
      'authenticationHealthy': instance.authenticationHealthy,
      'storageHealthy': instance.storageHealthy,
      'apiHealthy': instance.apiHealthy,
      'overallStatus': _$HealthStatusEnumMap[instance.overallStatus]!,
      'lastChecked': instance.lastChecked.toIso8601String(),
      'additionalMetrics': instance.additionalMetrics,
      'errorMessage': instance.errorMessage,
    };

const _$HealthStatusEnumMap = {
  HealthStatus.healthy: 'healthy',
  HealthStatus.degraded: 'degraded',
  HealthStatus.unhealthy: 'unhealthy',
  HealthStatus.unknown: 'unknown',
};

_$ComponentHealthStatusImpl _$$ComponentHealthStatusImplFromJson(
        Map<String, dynamic> json) =>
    _$ComponentHealthStatusImpl(
      name: json['name'] as String,
      status: $enumDecode(_$HealthStatusEnumMap, json['status']),
      lastChecked: DateTime.parse(json['lastChecked'] as String),
      errorMessage: json['errorMessage'] as String?,
      metrics: json['metrics'] as Map<String, dynamic>? ?? const {},
      responseTime: json['responseTime'] == null
          ? null
          : Duration(microseconds: (json['responseTime'] as num).toInt()),
      uptime: (json['uptime'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ComponentHealthStatusImplToJson(
        _$ComponentHealthStatusImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'status': _$HealthStatusEnumMap[instance.status]!,
      'lastChecked': instance.lastChecked.toIso8601String(),
      'errorMessage': instance.errorMessage,
      'metrics': instance.metrics,
      'responseTime': instance.responseTime?.inMicroseconds,
      'uptime': instance.uptime,
    };

_$HealthCheckResultImpl _$$HealthCheckResultImplFromJson(
        Map<String, dynamic> json) =>
    _$HealthCheckResultImpl(
      componentName: json['componentName'] as String,
      isHealthy: json['isHealthy'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
      responseTime: json['responseTime'] == null
          ? null
          : Duration(microseconds: (json['responseTime'] as num).toInt()),
      errorMessage: json['errorMessage'] as String?,
      details: json['details'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$HealthCheckResultImplToJson(
        _$HealthCheckResultImpl instance) =>
    <String, dynamic>{
      'componentName': instance.componentName,
      'isHealthy': instance.isHealthy,
      'timestamp': instance.timestamp.toIso8601String(),
      'responseTime': instance.responseTime?.inMicroseconds,
      'errorMessage': instance.errorMessage,
      'details': instance.details,
    };
