import 'package:freezed_annotation/freezed_annotation.dart';

part 'system_health_status.freezed.dart';
part 'system_health_status.g.dart';

/// [SystemHealthStatus] represents the overall health status of the system.
///
/// Used for monitoring system components and providing real-time health insights
/// in the admin panel dashboard.
@freezed
class SystemHealthStatus with _$SystemHealthStatus {
  /// Creates a [SystemHealthStatus] instance.
  const factory SystemHealthStatus({
    required bool networkConnected,
    required bool databaseHealthy,
    required bool authenticationHealthy,
    required bool storageHealthy,
    required bool apiHealthy,
    required HealthStatus overallStatus,
    required DateTime lastChecked,
    @Default({}) Map<String, dynamic> additionalMetrics,
    String? errorMessage,
  }) = _SystemHealthStatus;

  /// Creates a [SystemHealthStatus] from JSON.
  factory SystemHealthStatus.fromJson(Map<String, dynamic> json) =>
      _$SystemHealthStatusFromJson(json);
}

/// Extension methods for [SystemHealthStatus]
extension SystemHealthStatusX on SystemHealthStatus {
  /// Returns true if all critical systems are healthy
  bool get isHealthy =>
      networkConnected &&
      databaseHealthy &&
      authenticationHealthy &&
      storageHealthy &&
      apiHealthy;

  /// Returns the number of healthy components
  int get healthyComponentsCount {
    int count = 0;
    if (networkConnected) count++;
    if (databaseHealthy) count++;
    if (authenticationHealthy) count++;
    if (storageHealthy) count++;
    if (apiHealthy) count++;
    return count;
  }

  /// Returns the total number of components being monitored
  int get totalComponentsCount => 5;

  /// Returns the health percentage (0-100)
  double get healthPercentage =>
      (healthyComponentsCount / totalComponentsCount) * 100;

  /// Returns a list of unhealthy components
  List<String> get unhealthyComponents {
    final List<String> unhealthy = [];
    if (!networkConnected) unhealthy.add('Network');
    if (!databaseHealthy) unhealthy.add('Database');
    if (!authenticationHealthy) unhealthy.add('Authentication');
    if (!storageHealthy) unhealthy.add('Storage');
    if (!apiHealthy) unhealthy.add('API');
    return unhealthy;
  }

  /// Returns a human-readable status description
  String get statusDescription {
    switch (overallStatus) {
      case HealthStatus.healthy:
        return 'All systems operational';
      case HealthStatus.degraded:
        return 'Some systems experiencing issues: ${unhealthyComponents.join(', ')}';
      case HealthStatus.unhealthy:
        return 'Critical systems down: ${unhealthyComponents.join(', ')}';
      case HealthStatus.unknown:
        return 'System status unknown';
    }
  }

  /// Returns the appropriate color for the status
  String get statusColor {
    switch (overallStatus) {
      case HealthStatus.healthy:
        return '#4CAF50'; // Green
      case HealthStatus.degraded:
        return '#FF9800'; // Orange
      case HealthStatus.unhealthy:
        return '#F44336'; // Red
      case HealthStatus.unknown:
        return '#9E9E9E'; // Grey
    }
  }

  /// Returns the status icon
  String get statusIcon {
    switch (overallStatus) {
      case HealthStatus.healthy:
        return '✅';
      case HealthStatus.degraded:
        return '⚠️';
      case HealthStatus.unhealthy:
        return '❌';
      case HealthStatus.unknown:
        return '❓';
    }
  }
}

/// Enum representing different health status levels
enum HealthStatus {
  @JsonValue('healthy')
  healthy,
  @JsonValue('degraded')
  degraded,
  @JsonValue('unhealthy')
  unhealthy,
  @JsonValue('unknown')
  unknown,
}

/// Extension methods for [HealthStatus]
extension HealthStatusX on HealthStatus {
  /// Returns the display name for the status
  String get displayName {
    switch (this) {
      case HealthStatus.healthy:
        return 'Healthy';
      case HealthStatus.degraded:
        return 'Degraded';
      case HealthStatus.unhealthy:
        return 'Unhealthy';
      case HealthStatus.unknown:
        return 'Unknown';
    }
  }

  /// Returns the priority level (higher number = higher priority)
  int get priority {
    switch (this) {
      case HealthStatus.unhealthy:
        return 4;
      case HealthStatus.degraded:
        return 3;
      case HealthStatus.unknown:
        return 2;
      case HealthStatus.healthy:
        return 1;
    }
  }

  /// Returns true if this status requires immediate attention
  bool get requiresAttention {
    switch (this) {
      case HealthStatus.unhealthy:
      case HealthStatus.degraded:
        return true;
      case HealthStatus.healthy:
      case HealthStatus.unknown:
        return false;
    }
  }
}

/// [ComponentHealthStatus] represents the health status of an individual component.
@freezed
class ComponentHealthStatus with _$ComponentHealthStatus {
  /// Creates a [ComponentHealthStatus] instance.
  const factory ComponentHealthStatus({
    required String name,
    required HealthStatus status,
    required DateTime lastChecked,
    String? errorMessage,
    @Default({}) Map<String, dynamic> metrics,
    Duration? responseTime,
    double? uptime,
  }) = _ComponentHealthStatus;

  /// Creates a [ComponentHealthStatus] from JSON.
  factory ComponentHealthStatus.fromJson(Map<String, dynamic> json) =>
      _$ComponentHealthStatusFromJson(json);
}

/// [HealthCheckResult] represents the result of a health check operation.
@freezed
class HealthCheckResult with _$HealthCheckResult {
  /// Creates a [HealthCheckResult] instance.
  const factory HealthCheckResult({
    required String componentName,
    required bool isHealthy,
    required DateTime timestamp,
    Duration? responseTime,
    String? errorMessage,
    @Default({}) Map<String, dynamic> details,
  }) = _HealthCheckResult;

  /// Creates a [HealthCheckResult] from JSON.
  factory HealthCheckResult.fromJson(Map<String, dynamic> json) =>
      _$HealthCheckResultFromJson(json);
}

/// Predefined component names for health monitoring
class HealthComponents {
  static const String network = 'network';
  static const String database = 'database';
  static const String authentication = 'authentication';
  static const String storage = 'storage';
  static const String api = 'api';
  static const String cache = 'cache';
  static const String queue = 'queue';
  static const String scheduler = 'scheduler';
  static const String monitoring = 'monitoring';
  static const String analytics = 'analytics';
  static const String logging = 'logging';
  static const String backup = 'backup';
  static const String security = 'security';
  static const String performance = 'performance';
  static const String compliance = 'compliance';
}

/// Health check intervals and thresholds
class HealthThresholds {
  // Response time thresholds (in milliseconds)
  static const int fastResponseTime = 100;
  static const int normalResponseTime = 500;
  static const int slowResponseTime = 1000;
  static const int criticalResponseTime = 5000;

  // Uptime thresholds (as percentage)
  static const double excellentUptime = 99.9;
  static const double goodUptime = 99.5;
  static const double acceptableUptime = 99.0;
  static const double poorUptime = 95.0;

  // Memory usage thresholds (as percentage)
  static const double normalMemoryUsage = 70.0;
  static const double highMemoryUsage = 85.0;
  static const double criticalMemoryUsage = 95.0;

  // CPU usage thresholds (as percentage)
  static const double normalCpuUsage = 70.0;
  static const double highCpuUsage = 85.0;
  static const double criticalCpuUsage = 95.0;

  // Disk usage thresholds (as percentage)
  static const double normalDiskUsage = 80.0;
  static const double highDiskUsage = 90.0;
  static const double criticalDiskUsage = 95.0;

  // Health check intervals (in seconds)
  static const int fastCheckInterval = 30;
  static const int normalCheckInterval = 60;
  static const int slowCheckInterval = 300;
  static const int backgroundCheckInterval = 900;
}
