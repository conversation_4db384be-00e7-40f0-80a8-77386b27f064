import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'models/system_health_status.dart';

/// [SystemHealthService] monitors the health of various system components.
///
/// This service provides methods for:
/// - Checking network connectivity
/// - Monitoring database health
/// - Verifying authentication service status
/// - Providing overall system health status
/// - Running periodic health checks
///
/// Used for real-time system monitoring in the admin panel dashboard.
class SystemHealthService {
  final Connectivity _connectivity;
  final SupabaseClient _supabaseClient;
  final Logger _logger = Logger();

  Timer? _healthCheckTimer;
  bool _isMonitoring = false;
  
  final StreamController<SystemHealthStatus> _healthStatusController =
      StreamController<SystemHealthStatus>.broadcast();

  /// Creates a [SystemHealthService] instance.
  SystemHealthService(this._connectivity, this._supabaseClient);

  /// Stream of system health status updates.
  Stream<SystemHealthStatus> get healthStatusStream =>
      _healthStatusController.stream;

  /// Returns true if health monitoring is currently active.
  bool get isMonitoring => _isMonitoring;

  /// Checks network connectivity status.
  ///
  /// Returns true if connected to wifi, mobile, or ethernet.
  /// Returns false if no connection or connection check fails.
  Future<bool> checkNetworkConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      
      final isConnected = connectivityResults.any((result) =>
          result == ConnectivityResult.wifi ||
          result == ConnectivityResult.mobile ||
          result == ConnectivityResult.ethernet);

      _logger.d('Network connectivity check: $isConnected');
      return isConnected;
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to check network connectivity',
        error: error,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Checks database health by performing a simple query.
  ///
  /// Returns true if the database responds successfully.
  /// Returns false if the query fails or times out.
  Future<bool> checkDatabaseHealth() async {
    try {
      // Perform a simple health check query
      await _supabaseClient
          .from('health_check')
          .select('1')
          .limit(1)
          .timeout(const Duration(seconds: 5));

      _logger.d('Database health check: healthy');
      return true;
    } catch (error, stackTrace) {
      _logger.e(
        'Database health check failed',
        error: error,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Checks authentication service health.
  ///
  /// Returns true if the auth service responds successfully.
  /// Returns false if the service is unavailable or fails.
  Future<bool> checkAuthenticationHealth() async {
    try {
      // Check if auth service is responsive
      await _supabaseClient.auth
          .getUser()
          .timeout(const Duration(seconds: 5));

      _logger.d('Authentication health check: healthy');
      return true;
    } catch (error, stackTrace) {
      _logger.e(
        'Authentication health check failed',
        error: error,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Checks storage service health.
  ///
  /// Returns true if storage service is accessible.
  /// Returns false if the service is unavailable.
  Future<bool> checkStorageHealth() async {
    try {
      // Check if storage service is responsive
      await _supabaseClient.storage
          .listBuckets()
          .timeout(const Duration(seconds: 5));

      _logger.d('Storage health check: healthy');
      return true;
    } catch (error, stackTrace) {
      _logger.e(
        'Storage health check failed',
        error: error,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Checks API service health.
  ///
  /// Returns true if API endpoints are responsive.
  /// Returns false if APIs are unavailable.
  Future<bool> checkApiHealth() async {
    try {
      // Check if REST API is responsive
      await _supabaseClient
          .rest
          .from('health_check')
          .select('1')
          .limit(1)
          .timeout(const Duration(seconds: 5));

      _logger.d('API health check: healthy');
      return true;
    } catch (error, stackTrace) {
      _logger.e(
        'API health check failed',
        error: error,
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Gets the overall system health status.
  ///
  /// Performs all health checks and returns a comprehensive status.
  Future<SystemHealthStatus> getSystemHealthStatus() async {
    final timestamp = DateTime.now();
    
    try {
      // Perform all health checks concurrently
      final results = await Future.wait([
        checkNetworkConnectivity(),
        checkDatabaseHealth(),
        checkAuthenticationHealth(),
        checkStorageHealth(),
        checkApiHealth(),
      ]);

      final networkConnected = results[0];
      final databaseHealthy = results[1];
      final authenticationHealthy = results[2];
      final storageHealthy = results[3];
      final apiHealthy = results[4];

      // Determine overall status
      final healthyCount = results.where((result) => result).length;
      final totalChecks = results.length;

      HealthStatus overallStatus;
      if (healthyCount == totalChecks) {
        overallStatus = HealthStatus.healthy;
      } else if (healthyCount >= totalChecks * 0.6) {
        overallStatus = HealthStatus.degraded;
      } else {
        overallStatus = HealthStatus.unhealthy;
      }

      final status = SystemHealthStatus(
        networkConnected: networkConnected,
        databaseHealthy: databaseHealthy,
        authenticationHealthy: authenticationHealthy,
        storageHealthy: storageHealthy,
        apiHealthy: apiHealthy,
        overallStatus: overallStatus,
        lastChecked: timestamp,
        additionalMetrics: {
          'healthy_components': healthyCount,
          'total_components': totalChecks,
          'health_percentage': (healthyCount / totalChecks) * 100,
        },
      );

      _logger.i('System health check completed: ${overallStatus.displayName}');
      
      // Emit status update
      if (!_healthStatusController.isClosed) {
        _healthStatusController.add(status);
      }

      return status;
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to get system health status',
        error: error,
        stackTrace: stackTrace,
      );

      final errorStatus = SystemHealthStatus(
        networkConnected: false,
        databaseHealthy: false,
        authenticationHealthy: false,
        storageHealthy: false,
        apiHealthy: false,
        overallStatus: HealthStatus.unknown,
        lastChecked: timestamp,
        errorMessage: error.toString(),
      );

      if (!_healthStatusController.isClosed) {
        _healthStatusController.add(errorStatus);
      }

      return errorStatus;
    }
  }

  /// Starts periodic health monitoring.
  ///
  /// [interval] - The interval between health checks (default: 60 seconds)
  void startHealthMonitoring([Duration interval = const Duration(seconds: 60)]) {
    if (_isMonitoring) {
      _logger.w('Health monitoring is already running');
      return;
    }

    _isMonitoring = true;
    
    // Perform initial health check
    getSystemHealthStatus();

    // Start periodic health checks
    _healthCheckTimer = Timer.periodic(interval, (_) {
      if (_isMonitoring) {
        getSystemHealthStatus();
      }
    });

    _logger.i('Health monitoring started with ${interval.inSeconds}s interval');
  }

  /// Stops health monitoring.
  void stopHealthMonitoring() {
    if (!_isMonitoring) {
      _logger.w('Health monitoring is not running');
      return;
    }

    _isMonitoring = false;
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;

    _logger.i('Health monitoring stopped');
  }

  /// Performs a one-time health check for a specific component.
  ///
  /// [component] - The component to check
  /// Returns the health check result for the component.
  Future<HealthCheckResult> checkComponentHealth(String component) async {
    final timestamp = DateTime.now();
    final stopwatch = Stopwatch()..start();

    try {
      bool isHealthy;
      
      switch (component) {
        case HealthComponents.network:
          isHealthy = await checkNetworkConnectivity();
          break;
        case HealthComponents.database:
          isHealthy = await checkDatabaseHealth();
          break;
        case HealthComponents.authentication:
          isHealthy = await checkAuthenticationHealth();
          break;
        case HealthComponents.storage:
          isHealthy = await checkStorageHealth();
          break;
        case HealthComponents.api:
          isHealthy = await checkApiHealth();
          break;
        default:
          throw ArgumentError('Unknown component: $component');
      }

      stopwatch.stop();

      return HealthCheckResult(
        componentName: component,
        isHealthy: isHealthy,
        timestamp: timestamp,
        responseTime: stopwatch.elapsed,
      );
    } catch (error, stackTrace) {
      stopwatch.stop();
      
      _logger.e(
        'Component health check failed: $component',
        error: error,
        stackTrace: stackTrace,
      );

      return HealthCheckResult(
        componentName: component,
        isHealthy: false,
        timestamp: timestamp,
        responseTime: stopwatch.elapsed,
        errorMessage: error.toString(),
      );
    }
  }

  /// Disposes the health service and cleans up resources.
  void dispose() {
    stopHealthMonitoring();
    _healthStatusController.close();
    _logger.d('System health service disposed');
  }
}
