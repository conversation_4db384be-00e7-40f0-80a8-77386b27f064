import 'package:logger/logger.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

/// [AdminMonitoringService] handles error reporting and monitoring for the admin panel.
///
/// This service provides methods for:
/// - Reporting errors and exceptions to Sentry
/// - Adding breadcrumbs for debugging context
/// - Setting user context and tags for better error tracking
/// - Managing performance monitoring transactions
///
/// Uses Sentry for error reporting with comprehensive context tracking.
class AdminMonitoringService {
  final Hub _hub;
  final Logger _logger = Logger();

  /// Creates an [AdminMonitoringService] instance.
  AdminMonitoringService(this._hub);

  /// Reports an error with optional context.
  ///
  /// [error] - The error or exception to report
  /// [stackTrace] - The stack trace associated with the error
  /// [context] - Additional context information
  ///
  /// Handles reporting errors gracefully and logs them without throwing exceptions.
  Future<void> reportError(
    Object error,
    StackTrace stackTrace, [
    Map<String, dynamic>? context,
  ]) async {
    try {
      await _hub.captureException(
        error,
        stackTrace: stackTrace,
        withScope: (scope) {
          if (context != null) {
            for (final entry in context.entries) {
              scope.setExtra(entry.key, entry.value);
            }
          }

          // Add admin-specific tags
          scope.setTag('component', 'admin_panel');
          scope.setTag('error_source', 'admin_service');
        },
      );

      _logger.e(
        'Error reported to monitoring service',
        error: error,
        stackTrace: stackTrace,
      );
    } catch (reportingError, reportingStackTrace) {
      _logger.e(
        'Failed to report error to monitoring service',
        error: reportingError,
        stackTrace: reportingStackTrace,
      );
      // Don't rethrow - monitoring failures shouldn't break the app
    }
  }

  /// Reports a message with optional level and context.
  ///
  /// [message] - The message to report
  /// [level] - The severity level (defaults to info)
  /// [context] - Additional context information
  ///
  /// Handles reporting messages gracefully and logs them without throwing exceptions.
  Future<void> reportMessage(
    String message, [
    SentryLevel level = SentryLevel.info,
    Map<String, dynamic>? context,
  ]) async {
    try {
      await _hub.captureMessage(
        message,
        level: level,
        withScope: (scope) {
          if (context != null) {
            for (final entry in context.entries) {
              scope.setExtra(entry.key, entry.value);
            }
          }

          // Add admin-specific tags
          scope.setTag('component', 'admin_panel');
          scope.setTag('message_source', 'admin_service');
        },
      );

      _logger.i('Message reported to monitoring service: $message');
    } catch (reportingError, reportingStackTrace) {
      _logger.e(
        'Failed to report message to monitoring service',
        error: reportingError,
        stackTrace: reportingStackTrace,
      );
      // Don't rethrow - monitoring failures shouldn't break the app
    }
  }

  /// Adds a breadcrumb for debugging context.
  ///
  /// [message] - The breadcrumb message
  /// [category] - The category of the breadcrumb
  /// [data] - Additional data for the breadcrumb
  /// [level] - The severity level of the breadcrumb
  void addBreadcrumb(
    String message,
    String category, [
    Map<String, dynamic>? data,
    SentryLevel level = SentryLevel.info,
  ]) {
    try {
      final breadcrumb = Breadcrumb(
        message: message,
        category: category,
        data: data,
        level: level,
        timestamp: DateTime.now(),
      );

      _hub.addBreadcrumb(breadcrumb);

      _logger.d('Breadcrumb added: $category - $message');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to add breadcrumb',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - monitoring failures shouldn't break the app
    }
  }

  /// Sets user context for error reporting.
  ///
  /// [userId] - The user ID
  /// [email] - The user email (optional)
  /// [role] - The user role (optional)
  /// [additionalData] - Additional user data (optional)
  void setUserContext(
    String userId, [
    String? email,
    String? role,
    Map<String, dynamic>? additionalData,
  ]) {
    try {
      _hub.configureScope((scope) {
        final user = SentryUser(
          id: userId,
          email: email,
          data: {
            if (role != null) 'role': role,
            ...?additionalData,
          },
        );

        scope.setUser(user);
      });

      _logger.d('User context set for monitoring: $userId');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to set user context',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - monitoring failures shouldn't break the app
    }
  }

  /// Sets a tag for error reporting.
  ///
  /// [key] - The tag key
  /// [value] - The tag value
  void setTag(String key, String value) {
    try {
      _hub.configureScope((scope) {
        scope.setTag(key, value);
      });

      _logger.d('Tag set for monitoring: $key = $value');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to set tag',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - monitoring failures shouldn't break the app
    }
  }

  /// Sets extra data for error reporting.
  ///
  /// [key] - The extra data key
  /// [value] - The extra data value
  void setExtra(String key, dynamic value) {
    try {
      _hub.configureScope((scope) {
        scope.setExtra(key, value);
      });

      _logger.d('Extra data set for monitoring: $key');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to set extra data',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - monitoring failures shouldn't break the app
    }
  }

  /// Starts a performance monitoring transaction.
  ///
  /// [name] - The transaction name
  /// [operation] - The operation being performed
  ///
  /// Returns the transaction for further operations.
  ISentrySpan? startTransaction(String name, String operation) {
    try {
      final transaction = _hub.startTransaction(name, operation);

      _logger.d('Transaction started: $name ($operation)');

      return transaction;
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to start transaction',
        error: error,
        stackTrace: stackTrace,
      );
      // Don't rethrow - monitoring failures shouldn't break the app
      return null;
    }
  }

  /// Reports an admin-specific error with context.
  ///
  /// [error] - The error to report
  /// [stackTrace] - The stack trace
  /// [adminAction] - The admin action being performed
  /// [userId] - The admin user ID
  /// [additionalContext] - Additional context
  Future<void> reportAdminError(
    Object error,
    StackTrace stackTrace, {
    required String adminAction,
    required String userId,
    Map<String, dynamic>? additionalContext,
  }) async {
    final context = <String, dynamic>{
      'admin_action': adminAction,
      'admin_user_id': userId,
      'component': 'admin_panel',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalContext,
    };

    await reportError(error, stackTrace, context);
  }

  /// Reports a security-related incident.
  ///
  /// [incident] - Description of the security incident
  /// [severity] - The severity level
  /// [userId] - The user involved (if any)
  /// [ipAddress] - The IP address (if available)
  /// [additionalContext] - Additional context
  Future<void> reportSecurityIncident(
    String incident, {
    SentryLevel severity = SentryLevel.warning,
    String? userId,
    String? ipAddress,
    Map<String, dynamic>? additionalContext,
  }) async {
    final context = <String, dynamic>{
      'incident_type': 'security',
      'component': 'admin_panel',
      if (userId != null) 'user_id': userId,
      if (ipAddress != null) 'ip_address': ipAddress,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalContext,
    };

    await reportMessage(incident, severity, context);
  }

  /// Reports a performance issue.
  ///
  /// [operation] - The operation that had performance issues
  /// [duration] - The duration of the operation
  /// [threshold] - The expected threshold
  /// [additionalContext] - Additional context
  Future<void> reportPerformanceIssue(
    String operation, {
    required Duration duration,
    required Duration threshold,
    Map<String, dynamic>? additionalContext,
  }) async {
    final context = <String, dynamic>{
      'operation': operation,
      'duration_ms': duration.inMilliseconds,
      'threshold_ms': threshold.inMilliseconds,
      'performance_ratio': duration.inMilliseconds / threshold.inMilliseconds,
      'component': 'admin_panel',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalContext,
    };

    await reportMessage(
      'Performance issue detected: $operation took ${duration.inMilliseconds}ms (threshold: ${threshold.inMilliseconds}ms)',
      SentryLevel.warning,
      context,
    );
  }

  /// Clears user context.
  void clearUserContext() {
    try {
      _hub.configureScope((scope) {
        scope.setUser(null);
      });

      _logger.d('User context cleared from monitoring');
    } catch (error, stackTrace) {
      _logger.e(
        'Failed to clear user context',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Disposes the monitoring service.
  void dispose() {
    try {
      clearUserContext();
      _logger.d('Monitoring service disposed');
    } catch (error, stackTrace) {
      _logger.e(
        'Error during monitoring service disposal',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }
}
