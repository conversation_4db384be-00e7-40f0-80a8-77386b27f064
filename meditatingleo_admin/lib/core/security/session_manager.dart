import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:meditatingleo_admin/core/security/security_utils.dart';
import 'package:meditatingleo_admin/features/auth/data/services/secure_storage_service.dart';
import 'package:meditatingleo_admin/features/auth/data/models/admin_user_model.dart';

/// Manages admin user sessions with security features.
///
/// Provides session creation, validation, refresh, and cleanup functionality
/// with automatic timeout and security monitoring.
class SessionManager {
  SessionManager({
    required SecureStorageService storageService,
    Duration sessionTimeout = const Duration(hours: 8),
    Duration refreshThreshold = const Duration(minutes: 15),
  })  : _storageService = storageService,
        _sessionTimeout = sessionTimeout,
        _refreshThreshold = refreshThreshold;

  final SecureStorageService _storageService;
  final Duration _sessionTimeout;
  final Duration _refreshThreshold;

  Timer? _sessionTimer;
  Timer? _refreshTimer;
  AdminSession? _currentSession;

  final _sessionStateController = StreamController<SessionState>.broadcast();

  /// Stream of session state changes.
  Stream<SessionState> get sessionStateStream => _sessionStateController.stream;

  /// Current session state.
  SessionState get currentState {
    if (_currentSession == null) return SessionState.unauthenticated;
    if (_currentSession!.isExpired) return SessionState.expired;
    if (_currentSession!.needsRefresh(_refreshThreshold)) {
      return SessionState.needsRefresh;
    }
    return SessionState.authenticated;
  }

  /// Current session (if any).
  AdminSession? get currentSession => _currentSession;

  /// Creates a new admin session.
  ///
  /// [user] is the authenticated admin user.
  /// [accessToken] is the authentication token.
  /// [refreshToken] is the refresh token.
  /// [expiresAt] is when the session expires.
  Future<AdminSession> createSession({
    required AdminUserModel user,
    required String accessToken,
    required String refreshToken,
    required DateTime expiresAt,
  }) async {
    // Generate session ID and CSRF token
    final sessionId = SecurityUtils.generateSessionId();
    final csrfToken = SecurityUtils.generateCsrfToken();

    final session = AdminSession(
      sessionId: sessionId,
      user: user,
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresAt: expiresAt,
      createdAt: DateTime.now(),
      lastActivityAt: DateTime.now(),
      csrfToken: csrfToken,
    );

    // Store session data securely
    await _storeSession(session);

    // Set current session
    _currentSession = session;

    // Start session monitoring
    _startSessionMonitoring();

    // Emit session state change
    _sessionStateController.add(SessionState.authenticated);

    return session;
  }

  /// Restores a session from storage.
  ///
  /// Returns the restored session or null if no valid session exists.
  Future<AdminSession?> restoreSession() async {
    try {
      final sessionData = await _storageService.getSessionData();
      if (sessionData == null) return null;

      final session = AdminSession.fromJson(sessionData);

      // Check if session is still valid
      if (session.isExpired) {
        await clearSession();
        return null;
      }

      // Update last activity
      final updatedSession = session.copyWith(
        lastActivityAt: DateTime.now(),
      );

      _currentSession = updatedSession;
      await _storeSession(updatedSession);

      // Start session monitoring
      _startSessionMonitoring();

      // Emit appropriate session state
      if (updatedSession.needsRefresh(_refreshThreshold)) {
        _sessionStateController.add(SessionState.needsRefresh);
      } else {
        _sessionStateController.add(SessionState.authenticated);
      }

      return updatedSession;
    } catch (e) {
      // Clear invalid session data
      await clearSession();
      return null;
    }
  }

  /// Updates session activity timestamp.
  ///
  /// Should be called on user interactions to maintain session.
  Future<void> updateActivity() async {
    if (_currentSession == null) return;

    final updatedSession = _currentSession!.copyWith(
      lastActivityAt: DateTime.now(),
    );

    _currentSession = updatedSession;
    await _storeSession(updatedSession);
  }

  /// Refreshes the session with new tokens.
  ///
  /// [newAccessToken] is the new access token.
  /// [newRefreshToken] is the new refresh token (optional).
  /// [newExpiresAt] is the new expiration time.
  Future<AdminSession?> refreshSession({
    required String newAccessToken,
    String? newRefreshToken,
    required DateTime newExpiresAt,
  }) async {
    if (_currentSession == null) return null;

    final refreshedSession = _currentSession!.copyWith(
      accessToken: newAccessToken,
      refreshToken: newRefreshToken ?? _currentSession!.refreshToken,
      expiresAt: newExpiresAt,
      lastActivityAt: DateTime.now(),
    );

    _currentSession = refreshedSession;
    await _storeSession(refreshedSession);

    // Restart session monitoring with new expiration
    _startSessionMonitoring();

    _sessionStateController.add(SessionState.authenticated);

    return refreshedSession;
  }

  /// Validates the current session.
  ///
  /// Returns true if the session is valid and active.
  bool validateSession() {
    if (_currentSession == null) return false;
    if (_currentSession!.isExpired) return false;

    // Check for suspicious activity (e.g., too long since last activity)
    final timeSinceActivity =
        DateTime.now().difference(_currentSession!.lastActivityAt);
    if (timeSinceActivity > _sessionTimeout) {
      return false;
    }

    return true;
  }

  /// Validates a CSRF token against the current session.
  ///
  /// [token] is the CSRF token to validate.
  /// Returns true if the token is valid.
  bool validateCsrfToken(String token) {
    if (_currentSession == null) return false;
    return SecurityUtils.validateCsrfToken(token, _currentSession!.csrfToken);
  }

  /// Clears the current session and all stored data.
  Future<void> clearSession() async {
    _currentSession = null;
    _stopSessionMonitoring();

    await _storageService.clearSessionData();

    _sessionStateController.add(SessionState.unauthenticated);
  }

  /// Disposes the session manager and cleans up resources.
  void dispose() {
    _stopSessionMonitoring();
    _sessionStateController.close();
  }

  /// Stores session data securely.
  Future<void> _storeSession(AdminSession session) async {
    await _storageService.storeSessionData(session.toJson());
  }

  /// Starts session monitoring timers.
  void _startSessionMonitoring() {
    _stopSessionMonitoring();

    if (_currentSession == null) return;

    // Set timer for session expiration
    final timeUntilExpiry =
        _currentSession!.expiresAt.difference(DateTime.now());
    if (timeUntilExpiry.isNegative) {
      // Session already expired
      _sessionStateController.add(SessionState.expired);
      return;
    }

    _sessionTimer = Timer(timeUntilExpiry, () {
      _sessionStateController.add(SessionState.expired);
    });

    // Set timer for refresh threshold
    final timeUntilRefresh = _currentSession!.expiresAt
        .subtract(_refreshThreshold)
        .difference(DateTime.now());

    if (!timeUntilRefresh.isNegative) {
      _refreshTimer = Timer(timeUntilRefresh, () {
        _sessionStateController.add(SessionState.needsRefresh);
      });
    }
  }

  /// Stops session monitoring timers.
  void _stopSessionMonitoring() {
    _sessionTimer?.cancel();
    _refreshTimer?.cancel();
    _sessionTimer = null;
    _refreshTimer = null;
  }
}

/// Represents an admin user session.
class AdminSession {
  const AdminSession({
    required this.sessionId,
    required this.user,
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    required this.createdAt,
    required this.lastActivityAt,
    required this.csrfToken,
  });

  final String sessionId;
  final AdminUserModel user;
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final DateTime createdAt;
  final DateTime lastActivityAt;
  final String csrfToken;

  /// Whether the session has expired.
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Whether the session needs to be refreshed.
  bool needsRefresh(Duration threshold) {
    final refreshTime = expiresAt.subtract(threshold);
    return DateTime.now().isAfter(refreshTime);
  }

  /// Creates a copy with updated fields.
  AdminSession copyWith({
    String? sessionId,
    AdminUserModel? user,
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
    DateTime? createdAt,
    DateTime? lastActivityAt,
    String? csrfToken,
  }) {
    return AdminSession(
      sessionId: sessionId ?? this.sessionId,
      user: user ?? this.user,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      createdAt: createdAt ?? this.createdAt,
      lastActivityAt: lastActivityAt ?? this.lastActivityAt,
      csrfToken: csrfToken ?? this.csrfToken,
    );
  }

  /// Converts to JSON for storage.
  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'user': user.toJson(),
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'lastActivityAt': lastActivityAt.toIso8601String(),
      'csrfToken': csrfToken,
    };
  }

  /// Creates from JSON.
  factory AdminSession.fromJson(Map<String, dynamic> json) {
    return AdminSession(
      sessionId: json['sessionId'] as String,
      user: AdminUserModel.fromJson(json['user'] as Map<String, dynamic>),
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String,
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastActivityAt: DateTime.parse(json['lastActivityAt'] as String),
      csrfToken: json['csrfToken'] as String,
    );
  }
}

/// Session state enumeration.
enum SessionState {
  unauthenticated,
  authenticated,
  needsRefresh,
  expired,
}

/// Extension for session state display.
extension SessionStateX on SessionState {
  String get displayName {
    switch (this) {
      case SessionState.unauthenticated:
        return 'Not Authenticated';
      case SessionState.authenticated:
        return 'Authenticated';
      case SessionState.needsRefresh:
        return 'Needs Refresh';
      case SessionState.expired:
        return 'Expired';
    }
  }
}
