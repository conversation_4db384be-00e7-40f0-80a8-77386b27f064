import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

/// Utility class for security-related operations in the admin panel.
///
/// Provides methods for password validation, token generation, encryption,
/// and other security operations required for admin authentication.
class SecurityUtils {
  SecurityUtils._();

  /// Validates password strength according to admin security requirements.
  ///
  /// Returns a [PasswordValidationResult] with validation status and feedback.
  static PasswordValidationResult validatePassword(String password) {
    final errors = <String>[];

    // Minimum length check
    if (password.length < 12) {
      errors.add('Password must be at least 12 characters long');
    }

    // Maximum length check
    if (password.length > 128) {
      errors.add('Password must not exceed 128 characters');
    }

    // Character type requirements
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      errors.add('Password must contain at least one uppercase letter');
    }

    if (!RegExp(r'[a-z]').hasMatch(password)) {
      errors.add('Password must contain at least one lowercase letter');
    }

    if (!RegExp(r'[0-9]').hasMatch(password)) {
      errors.add('Password must contain at least one number');
    }

    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      errors.add('Password must contain at least one special character');
    }

    // Common password patterns
    if (RegExp(r'(.)\1{2,}').hasMatch(password)) {
      errors.add('Password must not contain repeated characters');
    }

    if (RegExp(
            r'(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)',
            caseSensitive: false)
        .hasMatch(password)) {
      errors.add('Password must not contain sequential characters');
    }

    // Calculate strength score
    int score = 0;
    if (password.length >= 12) score += 20;
    if (password.length >= 16) score += 20;
    if (RegExp(r'[A-Z]').hasMatch(password)) score += 15;
    if (RegExp(r'[a-z]').hasMatch(password)) score += 15;
    if (RegExp(r'[0-9]').hasMatch(password)) score += 15;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score += 15;
    if (password.length >= 20) score += 10;

    final strength = score >= 80
        ? PasswordStrength.strong
        : score >= 60
            ? PasswordStrength.medium
            : score >= 40
                ? PasswordStrength.weak
                : PasswordStrength.veryWeak;

    return PasswordValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      strength: strength,
      score: score,
    );
  }

  /// Generates a cryptographically secure random token.
  ///
  /// [length] specifies the number of bytes to generate (default: 32).
  /// Returns a base64-encoded string.
  static String generateSecureToken([int length = 32]) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }

  /// Generates a secure session ID for admin sessions.
  ///
  /// Returns a 64-character hexadecimal string.
  static String generateSessionId() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
  }

  /// Hashes a password using SHA-256 with salt.
  ///
  /// [password] is the plain text password to hash.
  /// [salt] is the salt to use (if not provided, a random salt is generated).
  /// Returns a [HashedPassword] object containing the hash and salt.
  static HashedPassword hashPassword(String password, [String? salt]) {
    salt ??= generateSecureToken(16);
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);

    return HashedPassword(
      hash: digest.toString(),
      salt: salt,
    );
  }

  /// Verifies a password against a hash.
  ///
  /// Returns true if the password matches the hash.
  static bool verifyPassword(String password, String hash, String salt) {
    final hashedPassword = hashPassword(password, salt);
    return hashedPassword.hash == hash;
  }

  /// Generates backup codes for MFA.
  ///
  /// [count] specifies the number of backup codes to generate (default: 10).
  /// Returns a list of 8-character alphanumeric codes.
  static List<String> generateBackupCodes([int count = 10]) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();

    return List.generate(count, (index) {
      return List.generate(8, (i) => chars[random.nextInt(chars.length)])
          .join();
    });
  }

  /// Validates an email address format.
  ///
  /// Returns true if the email format is valid.
  static bool isValidEmail(String email) {
    // Check for consecutive dots
    if (email.contains('..')) return false;
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// Sanitizes user input to prevent XSS attacks.
  ///
  /// Removes or escapes potentially dangerous characters.
  static String sanitizeInput(String input) {
    return input
        .replaceAll('&', '&amp;') // Must be first to avoid double escaping
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;');
  }

  /// Validates that a string contains only safe characters.
  ///
  /// Returns true if the string is safe for database operations.
  static bool isSafeString(String input) {
    // Allow alphanumeric, spaces, and common punctuation (but not newlines)
    return RegExp(
            r'^[a-zA-Z0-9 \.,!?\-_@#$%^&*()+=\[\]{}|\\:";' "'" '<>?/~`]*\$')
        .hasMatch(input);
  }

  /// Generates a CSRF token for form protection.
  ///
  /// Returns a base64-encoded token.
  static String generateCsrfToken() {
    return generateSecureToken(24);
  }

  /// Validates a CSRF token.
  ///
  /// [token] is the token to validate.
  /// [expectedToken] is the expected token value.
  /// Returns true if the tokens match.
  static bool validateCsrfToken(String token, String expectedToken) {
    return token == expectedToken && token.isNotEmpty;
  }
}

/// Result of password validation.
class PasswordValidationResult {
  const PasswordValidationResult({
    required this.isValid,
    required this.errors,
    required this.strength,
    required this.score,
  });

  final bool isValid;
  final List<String> errors;
  final PasswordStrength strength;
  final int score;
}

/// Password strength levels.
enum PasswordStrength {
  veryWeak,
  weak,
  medium,
  strong,
}

/// Extension for password strength display.
extension PasswordStrengthX on PasswordStrength {
  String get displayName {
    switch (this) {
      case PasswordStrength.veryWeak:
        return 'Very Weak';
      case PasswordStrength.weak:
        return 'Weak';
      case PasswordStrength.medium:
        return 'Medium';
      case PasswordStrength.strong:
        return 'Strong';
    }
  }

  String get color {
    switch (this) {
      case PasswordStrength.veryWeak:
        return '#F44336'; // Red
      case PasswordStrength.weak:
        return '#FF9800'; // Orange
      case PasswordStrength.medium:
        return '#FFC107'; // Amber
      case PasswordStrength.strong:
        return '#4CAF50'; // Green
    }
  }
}

/// Hashed password result.
class HashedPassword {
  const HashedPassword({
    required this.hash,
    required this.salt,
  });

  final String hash;
  final String salt;
}
