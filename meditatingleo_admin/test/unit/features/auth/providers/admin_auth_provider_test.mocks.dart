// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_admin/test/unit/features/auth/providers/admin_auth_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:meditatingleo_admin/features/auth/data/models/admin_user_model.dart'
    as _i5;
import 'package:meditatingleo_admin/features/auth/data/models/audit_log_model.dart'
    as _i7;
import 'package:meditatingleo_admin/features/auth/data/models/auth_response_model.dart'
    as _i2;
import 'package:meditatingleo_admin/features/auth/data/services/admin_auth_service.dart'
    as _i3;
import 'package:meditatingleo_admin/features/auth/data/services/audit_log_service.dart'
    as _i6;
import 'package:meditatingleo_admin/features/auth/data/services/secure_storage_service.dart'
    as _i8;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthResponseModel_0 extends _i1.SmartFake
    implements _i2.AuthResponseModel {
  _FakeAuthResponseModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AdminAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAdminAuthService extends _i1.Mock implements _i3.AdminAuthService {
  MockAdminAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.AuthResponseModel> signInWithEmailAndPassword(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithEmailAndPassword,
          [
            email,
            password,
          ],
        ),
        returnValue:
            _i4.Future<_i2.AuthResponseModel>.value(_FakeAuthResponseModel_0(
          this,
          Invocation.method(
            #signInWithEmailAndPassword,
            [
              email,
              password,
            ],
          ),
        )),
      ) as _i4.Future<_i2.AuthResponseModel>);

  @override
  _i4.Future<void> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i5.AdminUserModel?> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i4.Future<_i5.AdminUserModel?>.value(),
      ) as _i4.Future<_i5.AdminUserModel?>);

  @override
  _i4.Future<_i2.AuthResponseModel> refreshSession() => (super.noSuchMethod(
        Invocation.method(
          #refreshSession,
          [],
        ),
        returnValue:
            _i4.Future<_i2.AuthResponseModel>.value(_FakeAuthResponseModel_0(
          this,
          Invocation.method(
            #refreshSession,
            [],
          ),
        )),
      ) as _i4.Future<_i2.AuthResponseModel>);

  @override
  _i4.Future<void> changePassword(String? newPassword) => (super.noSuchMethod(
        Invocation.method(
          #changePassword,
          [newPassword],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  bool isValidAdminRole(Map<String, dynamic>? metadata) => (super.noSuchMethod(
        Invocation.method(
          #isValidAdminRole,
          [metadata],
        ),
        returnValue: false,
      ) as bool);
}

/// A class which mocks [AuditLogService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuditLogService extends _i1.Mock implements _i6.AuditLogService {
  MockAuditLogService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> logAuthEvent({
    required String? userId,
    required _i7.AuditAction? action,
    required bool? success,
    required Map<String, dynamic>? details,
    String? errorMessage,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logAuthEvent,
          [],
          {
            #userId: userId,
            #action: action,
            #success: success,
            #details: details,
            #errorMessage: errorMessage,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> logUserManagementEvent({
    required String? adminUserId,
    required String? targetUserId,
    required _i7.AuditAction? action,
    required bool? success,
    required Map<String, dynamic>? details,
    String? errorMessage,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logUserManagementEvent,
          [],
          {
            #adminUserId: adminUserId,
            #targetUserId: targetUserId,
            #action: action,
            #success: success,
            #details: details,
            #errorMessage: errorMessage,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> logSystemEvent({
    required String? adminUserId,
    required _i7.AuditAction? action,
    required bool? success,
    required Map<String, dynamic>? details,
    String? errorMessage,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logSystemEvent,
          [],
          {
            #adminUserId: adminUserId,
            #action: action,
            #success: success,
            #details: details,
            #errorMessage: errorMessage,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<List<_i7.AuditLogModel>> getAuditLogs({
    String? userId,
    _i7.AuditAction? action,
    DateTime? startDate,
    DateTime? endDate,
    bool? success,
    int? limit = 100,
    int? offset = 0,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAuditLogs,
          [],
          {
            #userId: userId,
            #action: action,
            #startDate: startDate,
            #endDate: endDate,
            #success: success,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue:
            _i4.Future<List<_i7.AuditLogModel>>.value(<_i7.AuditLogModel>[]),
      ) as _i4.Future<List<_i7.AuditLogModel>>);

  @override
  _i4.Future<List<_i7.AuditLogModel>> getSecurityEvents({
    DateTime? startDate,
    DateTime? endDate,
    int? limit = 50,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSecurityEvents,
          [],
          {
            #startDate: startDate,
            #endDate: endDate,
            #limit: limit,
          },
        ),
        returnValue:
            _i4.Future<List<_i7.AuditLogModel>>.value(<_i7.AuditLogModel>[]),
      ) as _i4.Future<List<_i7.AuditLogModel>>);

  @override
  _i4.Future<Map<String, dynamic>> getAuditStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAuditStatistics,
          [],
          {
            #startDate: startDate,
            #endDate: endDate,
          },
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);
}

/// A class which mocks [SecureStorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSecureStorageService extends _i1.Mock
    implements _i8.SecureStorageService {
  MockSecureStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> storeAuthTokens({
    required String? accessToken,
    required String? refreshToken,
    required String? expiresAt,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeAuthTokens,
          [],
          {
            #accessToken: accessToken,
            #refreshToken: refreshToken,
            #expiresAt: expiresAt,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<String?> getAccessToken() => (super.noSuchMethod(
        Invocation.method(
          #getAccessToken,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<String?> getRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #getRefreshToken,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<String?> getSessionExpiry() => (super.noSuchMethod(
        Invocation.method(
          #getSessionExpiry,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<void> storeUserInfo({
    required String? userId,
    required String? email,
    required String? role,
    required bool? mfaEnabled,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeUserInfo,
          [],
          {
            #userId: userId,
            #email: email,
            #role: role,
            #mfaEnabled: mfaEnabled,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<String?> getUserId() => (super.noSuchMethod(
        Invocation.method(
          #getUserId,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<String?> getUserEmail() => (super.noSuchMethod(
        Invocation.method(
          #getUserEmail,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<String?> getUserRole() => (super.noSuchMethod(
        Invocation.method(
          #getUserRole,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<bool> isMfaEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isMfaEnabled,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<String?> getLastLogin() => (super.noSuchMethod(
        Invocation.method(
          #getLastLogin,
          [],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<void> setBiometricEnabled(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setBiometricEnabled,
          [enabled],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> isBiometricEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isBiometricEnabled,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> isSessionValid() => (super.noSuchMethod(
        Invocation.method(
          #isSessionValid,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> hasStoredAuth() => (super.noSuchMethod(
        Invocation.method(
          #hasStoredAuth,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> clearAuthData() => (super.noSuchMethod(
        Invocation.method(
          #clearAuthData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> clearAllData() => (super.noSuchMethod(
        Invocation.method(
          #clearAllData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<Map<String, String>> getAllStoredData() => (super.noSuchMethod(
        Invocation.method(
          #getAllStoredData,
          [],
        ),
        returnValue: _i4.Future<Map<String, String>>.value(<String, String>{}),
      ) as _i4.Future<Map<String, String>>);

  @override
  _i4.Future<void> storeCustomData(
    String? key,
    String? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeCustomData,
          [
            key,
            value,
          ],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<String?> getCustomData(String? key) => (super.noSuchMethod(
        Invocation.method(
          #getCustomData,
          [key],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<void> deleteCustomData(String? key) => (super.noSuchMethod(
        Invocation.method(
          #deleteCustomData,
          [key],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> storeSessionData(Map<String, dynamic>? sessionData) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeSessionData,
          [sessionData],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<Map<String, dynamic>?> getSessionData() => (super.noSuchMethod(
        Invocation.method(
          #getSessionData,
          [],
        ),
        returnValue: _i4.Future<Map<String, dynamic>?>.value(),
      ) as _i4.Future<Map<String, dynamic>?>);

  @override
  _i4.Future<void> clearSessionData() => (super.noSuchMethod(
        Invocation.method(
          #clearSessionData,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}
