import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_admin/features/auth/data/services/mfa_service.dart';

import 'mfa_service_test.mocks.dart';

@GenerateMocks([SupabaseClient])
void main() {
  group('MfaService', () {
    late MfaService mfaService;
    late MockSupabaseClient mockSupabaseClient;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mfaService = MfaService(mockSupabaseClient);
    });

    group('Utility Methods', () {
      test('should generate backup codes correctly', () {
        // Act
        final backupCodes = mfaService.generateBackupCodes();

        // Assert
        expect(backupCodes, hasLength(5));
        for (final code in backupCodes) {
          expect(code, hasLength(6));
          expect(RegExp(r'^\d{6}$').hasMatch(code), isTrue);
        }

        // Ensure all codes are unique
        final uniqueCodes = backupCodes.toSet();
        expect(uniqueCodes.length, equals(backupCodes.length));
      });

      test('should validate TOTP code format correctly', () {
        const secret = 'JBSWY3DPEHPK3PXP';

        // Test valid 6-digit codes
        expect(mfaService.validateTotpCode(secret, '123456'), isA<bool>());
        expect(mfaService.validateTotpCode(secret, '000000'), isA<bool>());

        // The actual validation depends on time, so we just test the method exists
        // and returns a boolean
      });

      test('should generate TOTP code correctly', () {
        const secret = 'JBSWY3DPEHPK3PXP';
        const timeStep = 12345;

        // Act
        final code = mfaService.generateTotpCode(secret, timeStep);

        // Assert
        expect(code, hasLength(6));
        expect(RegExp(r'^\d{6}$').hasMatch(code), isTrue);

        // Same inputs should produce same output
        final code2 = mfaService.generateTotpCode(secret, timeStep);
        expect(code, equals(code2));
      });

      test('should decode base32 correctly', () {
        // Test with known base32 values
        final decoded = mfaService.base32Decode('JBSWY3DPEHPK3PXP');

        // Assert
        expect(decoded, isA<List<int>>());
        expect(decoded.isNotEmpty, isTrue);

        // Test with empty string
        final emptyDecoded = mfaService.base32Decode('');
        expect(emptyDecoded, isEmpty);
      });
    });
  });
}
