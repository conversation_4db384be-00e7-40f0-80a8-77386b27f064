import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthException;
import 'package:meditatingleo_admin/features/auth/data/services/admin_auth_service.dart';
import 'package:meditatingleo_admin/features/auth/data/models/admin_user_model.dart';
import 'package:meditatingleo_admin/features/auth/data/models/auth_response_model.dart';
import 'package:meditatingleo_admin/core/error/exceptions.dart';

import 'admin_auth_service_test.mocks.dart';

@GenerateMocks([SupabaseClient, GoTrueClient, AuthResponse, User, Session])
void main() {
  group('AdminAuthService', () {
    late AdminAuthService authService;
    late MockSupabaseClient mockSupabaseClient;
    late MockGoTrueClient mockAuth;
    late MockAuthResponse mockAuthResponse;
    late MockUser mockUser;
    late MockSession mockSession;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockAuthResponse = MockAuthResponse();
      mockUser = MockUser();
      mockSession = MockSession();

      when(mockSupabaseClient.auth).thenReturn(mockAuth);
      authService = AdminAuthService(mockSupabaseClient);
    });

    group('signInWithEmailAndPassword', () {
      test('should sign in successfully with valid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        when(mockAuth.signInWithPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockAuthResponse);

        when(mockAuthResponse.user).thenReturn(mockUser);
        when(mockAuthResponse.session).thenReturn(mockSession);

        when(mockUser.id).thenReturn('user-123');
        when(mockUser.email).thenReturn(email);
        when(mockUser.userMetadata).thenReturn({
          'role': 'content_manager',
          'is_active': true,
        });

        when(mockSession.accessToken).thenReturn('access-token');
        when(mockSession.refreshToken).thenReturn('refresh-token');
        when(mockSession.expiresAt).thenReturn(1234567890);

        // Act
        final result =
            await authService.signInWithEmailAndPassword(email, password);

        // Assert
        expect(result, isA<AuthResponseModel>());
        expect(result.user.email, email);
        expect(result.user.role, AdminRole.contentManager);
        expect(result.accessToken, 'access-token');
        expect(result.refreshToken, 'refresh-token');
        verify(mockAuth.signInWithPassword(email: email, password: password))
            .called(1);
      });

      test('should throw AuthException for invalid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';

        when(mockAuth.signInWithPassword(
          email: email,
          password: password,
        )).thenThrow(AuthException('Invalid credentials'));

        // Act & Assert
        expect(
          () => authService.signInWithEmailAndPassword(email, password),
          throwsA(isA<AuthException>()),
        );
      });

      test('should throw AuthException for inactive account', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        when(mockAuth.signInWithPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockAuthResponse);

        when(mockAuthResponse.user).thenReturn(mockUser);
        when(mockAuthResponse.session).thenReturn(mockSession);

        when(mockUser.id).thenReturn('user-123');
        when(mockUser.email).thenReturn(email);
        when(mockUser.userMetadata).thenReturn({
          'role': 'content_manager',
          'is_active': false, // Inactive account
        });

        when(mockAuth.signOut()).thenAnswer((_) async {});

        // Act & Assert
        await expectLater(
          () => authService.signInWithEmailAndPassword(email, password),
          throwsA(isA<AuthException>()),
        );
        verify(mockAuth.signOut()).called(1);
      });

      test('should require MFA for system admin role', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        when(mockAuth.signInWithPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockAuthResponse);

        when(mockAuthResponse.user).thenReturn(mockUser);
        when(mockAuthResponse.session).thenReturn(mockSession);

        when(mockUser.id).thenReturn('user-123');
        when(mockUser.email).thenReturn(email);
        when(mockUser.userMetadata).thenReturn({
          'role': 'system_admin',
          'is_active': true,
          'mfa_enabled': true,
        });

        when(mockSession.accessToken).thenReturn('access-token');
        when(mockSession.refreshToken).thenReturn('refresh-token');
        when(mockSession.expiresAt).thenReturn(1234567890);

        // Act
        final result =
            await authService.signInWithEmailAndPassword(email, password);

        // Assert
        expect(result.requiresMfa, isTrue);
        expect(result.mfaToken, isNotNull);
        expect(result.user.role, AdminRole.systemAdmin);
      });
    });

    group('getCurrentUser', () {
      test('should return current user when authenticated', () async {
        // Arrange
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.id).thenReturn('user-123');
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockUser.userMetadata).thenReturn({
          'role': 'content_manager',
          'is_active': true,
        });

        // Act
        final result = await authService.getCurrentUser();

        // Assert
        expect(result, isA<AdminUserModel>());
        expect(result!.email, '<EMAIL>');
        expect(result.role, AdminRole.contentManager);
      });

      test('should return null when not authenticated', () async {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = await authService.getCurrentUser();

        // Assert
        expect(result, isNull);
      });
    });

    group('signOut', () {
      test('should sign out successfully', () async {
        // Arrange
        when(mockAuth.signOut()).thenAnswer((_) async {});

        // Act
        await authService.signOut();

        // Assert
        verify(mockAuth.signOut()).called(1);
      });

      test('should handle sign out errors gracefully', () async {
        // Arrange
        when(mockAuth.signOut()).thenThrow(Exception('Sign out failed'));

        // Act & Assert
        expect(
          () => authService.signOut(),
          throwsA(isA<AuthException>()),
        );
      });
    });
  });
}
