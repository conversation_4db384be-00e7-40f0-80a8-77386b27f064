// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_admin/test/unit/core/security/session_manager_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:meditatingleo_admin/features/auth/data/services/secure_storage_service.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [SecureStorageService].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockSecureStorageService extends _i1.Mock
    implements _i2.SecureStorageService {
  MockSecureStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> storeAuthTokens({
    required String? accessToken,
    required String? refreshToken,
    required String? expiresAt,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeAuthTokens,
          [],
          {
            #accessToken: accessToken,
            #refreshToken: refreshToken,
            #expiresAt: expiresAt,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<String?> getAccessToken() => (super.noSuchMethod(
        Invocation.method(
          #getAccessToken,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<String?> getRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #getRefreshToken,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<String?> getSessionExpiry() => (super.noSuchMethod(
        Invocation.method(
          #getSessionExpiry,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<void> storeUserInfo({
    required String? userId,
    required String? email,
    required String? role,
    required bool? mfaEnabled,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeUserInfo,
          [],
          {
            #userId: userId,
            #email: email,
            #role: role,
            #mfaEnabled: mfaEnabled,
          },
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<String?> getUserId() => (super.noSuchMethod(
        Invocation.method(
          #getUserId,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<String?> getUserEmail() => (super.noSuchMethod(
        Invocation.method(
          #getUserEmail,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<String?> getUserRole() => (super.noSuchMethod(
        Invocation.method(
          #getUserRole,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<bool> isMfaEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isMfaEnabled,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<String?> getLastLogin() => (super.noSuchMethod(
        Invocation.method(
          #getLastLogin,
          [],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<void> setBiometricEnabled(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setBiometricEnabled,
          [enabled],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<bool> isBiometricEnabled() => (super.noSuchMethod(
        Invocation.method(
          #isBiometricEnabled,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> isSessionValid() => (super.noSuchMethod(
        Invocation.method(
          #isSessionValid,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> hasStoredAuth() => (super.noSuchMethod(
        Invocation.method(
          #hasStoredAuth,
          [],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<void> clearAuthData() => (super.noSuchMethod(
        Invocation.method(
          #clearAuthData,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> clearAllData() => (super.noSuchMethod(
        Invocation.method(
          #clearAllData,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<Map<String, String>> getAllStoredData() => (super.noSuchMethod(
        Invocation.method(
          #getAllStoredData,
          [],
        ),
        returnValue: _i3.Future<Map<String, String>>.value(<String, String>{}),
      ) as _i3.Future<Map<String, String>>);

  @override
  _i3.Future<void> storeCustomData(
    String? key,
    String? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeCustomData,
          [
            key,
            value,
          ],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<String?> getCustomData(String? key) => (super.noSuchMethod(
        Invocation.method(
          #getCustomData,
          [key],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<void> deleteCustomData(String? key) => (super.noSuchMethod(
        Invocation.method(
          #deleteCustomData,
          [key],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> storeSessionData(Map<String, dynamic>? sessionData) =>
      (super.noSuchMethod(
        Invocation.method(
          #storeSessionData,
          [sessionData],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<Map<String, dynamic>?> getSessionData() => (super.noSuchMethod(
        Invocation.method(
          #getSessionData,
          [],
        ),
        returnValue: _i3.Future<Map<String, dynamic>?>.value(),
      ) as _i3.Future<Map<String, dynamic>?>);

  @override
  _i3.Future<void> clearSessionData() => (super.noSuchMethod(
        Invocation.method(
          #clearSessionData,
          [],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);
}
