import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:meditatingleo_admin/core/security/session_manager.dart';
import 'package:meditatingleo_admin/features/auth/data/services/secure_storage_service.dart';
import 'package:meditatingleo_admin/features/auth/data/models/admin_user_model.dart';

import 'session_manager_test.mocks.dart';

@GenerateMocks([SecureStorageService])
void main() {
  group('SessionManager', () {
    late SessionManager sessionManager;
    late MockSecureStorageService mockStorageService;
    late AdminUserModel testUser;

    setUp(() {
      mockStorageService = MockSecureStorageService();
      sessionManager = SessionManager(
        storageService: mockStorageService,
        sessionTimeout: const Duration(hours: 8),
        refreshThreshold: const Duration(minutes: 15),
      );
      
      testUser = AdminUserModel(
        id: 'user-123',
        email: '<EMAIL>',
        role: AdminRole.contentManager,
        isActive: true,
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      );
    });

    tearDown(() {
      sessionManager.dispose();
    });

    group('createSession', () {
      test('should create session successfully', () async {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});

        // Act
        final session = await sessionManager.createSession(
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiresAt,
        );

        // Assert
        expect(session.user, equals(testUser));
        expect(session.accessToken, equals('access-token'));
        expect(session.refreshToken, equals('refresh-token'));
        expect(session.expiresAt, equals(expiresAt));
        expect(session.sessionId, isNotEmpty);
        expect(session.csrfToken, isNotEmpty);
        expect(sessionManager.currentSession, equals(session));
        expect(sessionManager.currentState, equals(SessionState.authenticated));
        
        verify(mockStorageService.storeSessionData(any)).called(1);
      });

      test('should emit session state changes', () async {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        final stateChanges = <SessionState>[];
        
        sessionManager.sessionStateStream.listen(stateChanges.add);
        
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});

        // Act
        await sessionManager.createSession(
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiresAt,
        );

        // Wait for stream emission
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        expect(stateChanges, contains(SessionState.authenticated));
      });
    });

    group('restoreSession', () {
      test('should restore valid session from storage', () async {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        final sessionData = {
          'sessionId': 'session-123',
          'user': testUser.toJson(),
          'accessToken': 'access-token',
          'refreshToken': 'refresh-token',
          'expiresAt': expiresAt.toIso8601String(),
          'createdAt': DateTime.now().toIso8601String(),
          'lastActivityAt': DateTime.now().toIso8601String(),
          'csrfToken': 'csrf-token',
        };
        
        when(mockStorageService.getSessionData())
            .thenAnswer((_) async => sessionData);
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});

        // Act
        final session = await sessionManager.restoreSession();

        // Assert
        expect(session, isNotNull);
        expect(session!.user.id, equals(testUser.id));
        expect(session.accessToken, equals('access-token'));
        expect(sessionManager.currentSession, equals(session));
        
        verify(mockStorageService.getSessionData()).called(1);
        verify(mockStorageService.storeSessionData(any)).called(1); // Updated activity
      });

      test('should return null for expired session', () async {
        // Arrange
        final expiredTime = DateTime.now().subtract(const Duration(hours: 1));
        final sessionData = {
          'sessionId': 'session-123',
          'user': testUser.toJson(),
          'accessToken': 'access-token',
          'refreshToken': 'refresh-token',
          'expiresAt': expiredTime.toIso8601String(),
          'createdAt': DateTime.now().toIso8601String(),
          'lastActivityAt': DateTime.now().toIso8601String(),
          'csrfToken': 'csrf-token',
        };
        
        when(mockStorageService.getSessionData())
            .thenAnswer((_) async => sessionData);
        when(mockStorageService.clearSessionData())
            .thenAnswer((_) async {});

        // Act
        final session = await sessionManager.restoreSession();

        // Assert
        expect(session, isNull);
        expect(sessionManager.currentSession, isNull);
        
        verify(mockStorageService.clearSessionData()).called(1);
      });

      test('should return null when no session data exists', () async {
        // Arrange
        when(mockStorageService.getSessionData())
            .thenAnswer((_) async => null);

        // Act
        final session = await sessionManager.restoreSession();

        // Assert
        expect(session, isNull);
        expect(sessionManager.currentSession, isNull);
      });

      test('should clear invalid session data', () async {
        // Arrange
        when(mockStorageService.getSessionData())
            .thenThrow(Exception('Invalid session data'));
        when(mockStorageService.clearSessionData())
            .thenAnswer((_) async {});

        // Act
        final session = await sessionManager.restoreSession();

        // Assert
        expect(session, isNull);
        verify(mockStorageService.clearSessionData()).called(1);
      });
    });

    group('updateActivity', () {
      test('should update session activity timestamp', () async {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});

        final session = await sessionManager.createSession(
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiresAt,
        );
        
        final originalActivity = session.lastActivityAt;
        
        // Wait a bit to ensure timestamp difference
        await Future.delayed(const Duration(milliseconds: 10));

        // Act
        await sessionManager.updateActivity();

        // Assert
        expect(sessionManager.currentSession!.lastActivityAt.isAfter(originalActivity), isTrue);
        verify(mockStorageService.storeSessionData(any)).called(2); // Create + update
      });

      test('should do nothing when no session exists', () async {
        // Act
        await sessionManager.updateActivity();

        // Assert
        expect(sessionManager.currentSession, isNull);
        verifyNever(mockStorageService.storeSessionData(any));
      });
    });

    group('refreshSession', () {
      test('should refresh session with new tokens', () async {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        final newExpiresAt = DateTime.now().add(const Duration(hours: 2));
        
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});

        await sessionManager.createSession(
          user: testUser,
          accessToken: 'old-access-token',
          refreshToken: 'old-refresh-token',
          expiresAt: expiresAt,
        );

        // Act
        final refreshedSession = await sessionManager.refreshSession(
          newAccessToken: 'new-access-token',
          newRefreshToken: 'new-refresh-token',
          newExpiresAt: newExpiresAt,
        );

        // Assert
        expect(refreshedSession, isNotNull);
        expect(refreshedSession!.accessToken, equals('new-access-token'));
        expect(refreshedSession.refreshToken, equals('new-refresh-token'));
        expect(refreshedSession.expiresAt, equals(newExpiresAt));
        expect(sessionManager.currentSession, equals(refreshedSession));
      });

      test('should return null when no session exists', () async {
        // Act
        final refreshedSession = await sessionManager.refreshSession(
          newAccessToken: 'new-access-token',
          newExpiresAt: DateTime.now().add(const Duration(hours: 1)),
        );

        // Assert
        expect(refreshedSession, isNull);
      });
    });

    group('validateSession', () {
      test('should validate active session', () async {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});

        await sessionManager.createSession(
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiresAt,
        );

        // Act
        final isValid = sessionManager.validateSession();

        // Assert
        expect(isValid, isTrue);
      });

      test('should reject expired session', () async {
        // Arrange
        final expiredTime = DateTime.now().subtract(const Duration(hours: 1));
        
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});

        await sessionManager.createSession(
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiredTime,
        );

        // Act
        final isValid = sessionManager.validateSession();

        // Assert
        expect(isValid, isFalse);
      });

      test('should reject when no session exists', () {
        // Act
        final isValid = sessionManager.validateSession();

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('validateCsrfToken', () {
      test('should validate correct CSRF token', () async {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});

        final session = await sessionManager.createSession(
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiresAt,
        );

        // Act
        final isValid = sessionManager.validateCsrfToken(session.csrfToken);

        // Assert
        expect(isValid, isTrue);
      });

      test('should reject incorrect CSRF token', () async {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});

        await sessionManager.createSession(
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiresAt,
        );

        // Act
        final isValid = sessionManager.validateCsrfToken('wrong-token');

        // Assert
        expect(isValid, isFalse);
      });

      test('should reject when no session exists', () {
        // Act
        final isValid = sessionManager.validateCsrfToken('any-token');

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('clearSession', () {
      test('should clear session and storage', () async {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        
        when(mockStorageService.storeSessionData(any))
            .thenAnswer((_) async {});
        when(mockStorageService.clearSessionData())
            .thenAnswer((_) async {});

        await sessionManager.createSession(
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiresAt,
        );

        // Act
        await sessionManager.clearSession();

        // Assert
        expect(sessionManager.currentSession, isNull);
        expect(sessionManager.currentState, equals(SessionState.unauthenticated));
        
        verify(mockStorageService.clearSessionData()).called(1);
      });
    });
  });

  group('AdminSession', () {
    late AdminUserModel testUser;

    setUp(() {
      testUser = AdminUserModel(
        id: 'user-123',
        email: '<EMAIL>',
        role: AdminRole.contentManager,
        isActive: true,
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      );
    });

    group('isExpired', () {
      test('should return true for expired session', () {
        // Arrange
        final expiredTime = DateTime.now().subtract(const Duration(hours: 1));
        final session = AdminSession(
          sessionId: 'session-123',
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiredTime,
          createdAt: DateTime.now(),
          lastActivityAt: DateTime.now(),
          csrfToken: 'csrf-token',
        );

        // Act & Assert
        expect(session.isExpired, isTrue);
      });

      test('should return false for active session', () {
        // Arrange
        final futureTime = DateTime.now().add(const Duration(hours: 1));
        final session = AdminSession(
          sessionId: 'session-123',
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: futureTime,
          createdAt: DateTime.now(),
          lastActivityAt: DateTime.now(),
          csrfToken: 'csrf-token',
        );

        // Act & Assert
        expect(session.isExpired, isFalse);
      });
    });

    group('needsRefresh', () {
      test('should return true when within refresh threshold', () {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(minutes: 10));
        final session = AdminSession(
          sessionId: 'session-123',
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiresAt,
          createdAt: DateTime.now(),
          lastActivityAt: DateTime.now(),
          csrfToken: 'csrf-token',
        );

        // Act & Assert
        expect(session.needsRefresh(const Duration(minutes: 15)), isTrue);
      });

      test('should return false when outside refresh threshold', () {
        // Arrange
        final expiresAt = DateTime.now().add(const Duration(hours: 1));
        final session = AdminSession(
          sessionId: 'session-123',
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: expiresAt,
          createdAt: DateTime.now(),
          lastActivityAt: DateTime.now(),
          csrfToken: 'csrf-token',
        );

        // Act & Assert
        expect(session.needsRefresh(const Duration(minutes: 15)), isFalse);
      });
    });

    group('JSON serialization', () {
      test('should serialize and deserialize correctly', () {
        // Arrange
        final session = AdminSession(
          sessionId: 'session-123',
          user: testUser,
          accessToken: 'access-token',
          refreshToken: 'refresh-token',
          expiresAt: DateTime.now().add(const Duration(hours: 1)),
          createdAt: DateTime.now(),
          lastActivityAt: DateTime.now(),
          csrfToken: 'csrf-token',
        );

        // Act
        final json = session.toJson();
        final restored = AdminSession.fromJson(json);

        // Assert
        expect(restored.sessionId, equals(session.sessionId));
        expect(restored.user.id, equals(session.user.id));
        expect(restored.accessToken, equals(session.accessToken));
        expect(restored.refreshToken, equals(session.refreshToken));
        expect(restored.csrfToken, equals(session.csrfToken));
      });
    });
  });
}
