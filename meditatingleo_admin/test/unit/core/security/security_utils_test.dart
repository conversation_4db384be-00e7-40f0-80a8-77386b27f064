import 'package:flutter_test/flutter_test.dart';
import 'package:meditatingleo_admin/core/security/security_utils.dart';

void main() {
  group('SecurityUtils', () {
    group('validatePassword', () {
      test('should validate strong password correctly', () {
        // Arrange
        const strongPassword = 'MyStr0ng!P@ssw0rd987';

        // Act
        final result = SecurityUtils.validatePassword(strongPassword);

        // Assert
        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
        expect(result.strength, PasswordStrength.strong);
        expect(result.score, greaterThanOrEqualTo(80));
      });

      test('should reject password that is too short', () {
        // Arrange
        const shortPassword = 'Short1!';

        // Act
        final result = SecurityUtils.validatePassword(shortPassword);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors,
            contains('Password must be at least 12 characters long'));
        expect(result.strength, PasswordStrength.medium);
      });

      test('should reject password without uppercase letters', () {
        // Arrange
        const noUppercasePassword = 'mypassword123!';

        // Act
        final result = SecurityUtils.validatePassword(noUppercasePassword);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors,
            contains('Password must contain at least one uppercase letter'));
      });

      test('should reject password without lowercase letters', () {
        // Arrange
        const noLowercasePassword = 'MYPASSWORD123!';

        // Act
        final result = SecurityUtils.validatePassword(noLowercasePassword);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors,
            contains('Password must contain at least one lowercase letter'));
      });

      test('should reject password without numbers', () {
        // Arrange
        const noNumberPassword = 'MyPassword!';

        // Act
        final result = SecurityUtils.validatePassword(noNumberPassword);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors,
            contains('Password must contain at least one number'));
      });

      test('should reject password without special characters', () {
        // Arrange
        const noSpecialPassword = 'MyPassword123';

        // Act
        final result = SecurityUtils.validatePassword(noSpecialPassword);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors,
            contains('Password must contain at least one special character'));
      });

      test('should reject password with repeated characters', () {
        // Arrange
        const repeatedPassword = 'MyPasssssword123!';

        // Act
        final result = SecurityUtils.validatePassword(repeatedPassword);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors,
            contains('Password must not contain repeated characters'));
      });

      test('should reject password with sequential characters', () {
        // Arrange
        const sequentialPassword = 'MyPassword123!abc';

        // Act
        final result = SecurityUtils.validatePassword(sequentialPassword);

        // Assert
        expect(result.isValid, isFalse);
        expect(result.errors,
            contains('Password must not contain sequential characters'));
      });

      test('should reject password that is too long', () {
        // Arrange
        final tooLongPassword = 'A' * 129 + '1!';

        // Act
        final result = SecurityUtils.validatePassword(tooLongPassword);

        // Assert
        expect(result.isValid, isFalse);
        expect(
            result.errors, contains('Password must not exceed 128 characters'));
      });
    });

    group('generateSecureToken', () {
      test('should generate token with default length', () {
        // Act
        final token = SecurityUtils.generateSecureToken();

        // Assert
        expect(token, isNotEmpty);
        expect(
            token.length, greaterThan(40)); // Base64 encoding increases length
      });

      test('should generate token with custom length', () {
        // Act
        final token = SecurityUtils.generateSecureToken(16);

        // Assert
        expect(token, isNotEmpty);
        expect(
            token.length, greaterThan(20)); // Base64 encoding increases length
      });

      test('should generate different tokens each time', () {
        // Act
        final token1 = SecurityUtils.generateSecureToken();
        final token2 = SecurityUtils.generateSecureToken();

        // Assert
        expect(token1, isNot(equals(token2)));
      });
    });

    group('generateSessionId', () {
      test('should generate 64-character hexadecimal session ID', () {
        // Act
        final sessionId = SecurityUtils.generateSessionId();

        // Assert
        expect(sessionId, hasLength(64));
        expect(RegExp(r'^[0-9a-f]+$').hasMatch(sessionId), isTrue);
      });

      test('should generate different session IDs each time', () {
        // Act
        final sessionId1 = SecurityUtils.generateSessionId();
        final sessionId2 = SecurityUtils.generateSessionId();

        // Assert
        expect(sessionId1, isNot(equals(sessionId2)));
      });
    });

    group('hashPassword', () {
      test('should hash password with provided salt', () {
        // Arrange
        const password = 'MyPassword123!';
        const salt = 'testsalt';

        // Act
        final result = SecurityUtils.hashPassword(password, salt);

        // Assert
        expect(result.hash, isNotEmpty);
        expect(result.salt, equals(salt));
        expect(result.hash.length,
            equals(64)); // SHA-256 produces 64-character hex string
      });

      test('should hash password with generated salt when none provided', () {
        // Arrange
        const password = 'MyPassword123!';

        // Act
        final result = SecurityUtils.hashPassword(password);

        // Assert
        expect(result.hash, isNotEmpty);
        expect(result.salt, isNotEmpty);
        expect(result.hash.length, equals(64));
      });

      test('should produce same hash for same password and salt', () {
        // Arrange
        const password = 'MyPassword123!';
        const salt = 'testsalt';

        // Act
        final result1 = SecurityUtils.hashPassword(password, salt);
        final result2 = SecurityUtils.hashPassword(password, salt);

        // Assert
        expect(result1.hash, equals(result2.hash));
        expect(result1.salt, equals(result2.salt));
      });

      test('should produce different hashes for different passwords', () {
        // Arrange
        const password1 = 'MyPassword123!';
        const password2 = 'DifferentPassword456@';
        const salt = 'testsalt';

        // Act
        final result1 = SecurityUtils.hashPassword(password1, salt);
        final result2 = SecurityUtils.hashPassword(password2, salt);

        // Assert
        expect(result1.hash, isNot(equals(result2.hash)));
      });
    });

    group('verifyPassword', () {
      test('should verify correct password', () {
        // Arrange
        const password = 'MyPassword123!';
        const salt = 'testsalt';
        final hashedPassword = SecurityUtils.hashPassword(password, salt);

        // Act
        final isValid =
            SecurityUtils.verifyPassword(password, hashedPassword.hash, salt);

        // Assert
        expect(isValid, isTrue);
      });

      test('should reject incorrect password', () {
        // Arrange
        const correctPassword = 'MyPassword123!';
        const incorrectPassword = 'WrongPassword456@';
        const salt = 'testsalt';
        final hashedPassword =
            SecurityUtils.hashPassword(correctPassword, salt);

        // Act
        final isValid = SecurityUtils.verifyPassword(
            incorrectPassword, hashedPassword.hash, salt);

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('generateBackupCodes', () {
      test('should generate default number of backup codes', () {
        // Act
        final codes = SecurityUtils.generateBackupCodes();

        // Assert
        expect(codes, hasLength(10));
        codes.forEach((code) {
          expect(code, hasLength(8));
          expect(RegExp(r'^[A-Z0-9]+$').hasMatch(code), isTrue);
        });
      });

      test('should generate custom number of backup codes', () {
        // Act
        final codes = SecurityUtils.generateBackupCodes(5);

        // Assert
        expect(codes, hasLength(5));
        codes.forEach((code) {
          expect(code, hasLength(8));
          expect(RegExp(r'^[A-Z0-9]+$').hasMatch(code), isTrue);
        });
      });

      test('should generate unique backup codes', () {
        // Act
        final codes = SecurityUtils.generateBackupCodes(20);

        // Assert
        final uniqueCodes = codes.toSet();
        expect(uniqueCodes.length, equals(codes.length));
      });
    });

    group('isValidEmail', () {
      test('should validate correct email addresses', () {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in validEmails) {
          expect(SecurityUtils.isValidEmail(email), isTrue,
              reason: 'Email: $email');
        }
      });

      test('should reject invalid email addresses', () {
        const invalidEmails = [
          'invalid-email',
          '@example.com',
          'user@',
          'user@.com',
          '<EMAIL>',
          'user@example',
          '',
        ];

        for (final email in invalidEmails) {
          expect(SecurityUtils.isValidEmail(email), isFalse,
              reason: 'Email: $email');
        }
      });
    });

    group('sanitizeInput', () {
      test('should sanitize HTML characters', () {
        // Arrange
        const input = '<script>alert("xss")</script>';

        // Act
        final sanitized = SecurityUtils.sanitizeInput(input);

        // Assert
        expect(sanitized,
            equals('&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;'));
      });

      test('should sanitize quotes and ampersands', () {
        // Arrange
        const input = 'Hello "world" & \'universe\'';

        // Act
        final sanitized = SecurityUtils.sanitizeInput(input);

        // Assert
        expect(sanitized,
            equals('Hello &quot;world&quot; &amp; &#x27;universe&#x27;'));
      });
    });

    group('isSafeString', () {
      test('should accept safe strings', () {
        const safeStrings = [
          'Hello World',
          '<EMAIL>',
          'Password123!',
          'Some text with numbers 123',
        ];

        for (final str in safeStrings) {
          expect(SecurityUtils.isSafeString(str), isTrue,
              reason: 'String: $str');
        }
      });

      test('should reject unsafe strings', () {
        const unsafeStrings = [
          'Hello\nWorld', // Contains newline
          'Hello\tWorld', // Contains tab (should be allowed actually)
          'Hello\x00World', // Contains null byte
        ];

        for (final str in unsafeStrings) {
          if (str.contains('\t')) continue; // Tab should be allowed
          expect(SecurityUtils.isSafeString(str), isFalse,
              reason: 'String: $str');
        }
      });
    });

    group('CSRF token operations', () {
      test('should generate CSRF token', () {
        // Act
        final token = SecurityUtils.generateCsrfToken();

        // Assert
        expect(token, isNotEmpty);
        expect(token.length, greaterThan(30));
      });

      test('should validate matching CSRF tokens', () {
        // Arrange
        final token = SecurityUtils.generateCsrfToken();

        // Act
        final isValid = SecurityUtils.validateCsrfToken(token, token);

        // Assert
        expect(isValid, isTrue);
      });

      test('should reject non-matching CSRF tokens', () {
        // Arrange
        final token1 = SecurityUtils.generateCsrfToken();
        final token2 = SecurityUtils.generateCsrfToken();

        // Act
        final isValid = SecurityUtils.validateCsrfToken(token1, token2);

        // Assert
        expect(isValid, isFalse);
      });

      test('should reject empty CSRF tokens', () {
        // Act
        final isValid = SecurityUtils.validateCsrfToken('', '');

        // Assert
        expect(isValid, isFalse);
      });
    });
  });

  group('PasswordStrengthX', () {
    test('should provide correct display names', () {
      expect(PasswordStrength.veryWeak.displayName, equals('Very Weak'));
      expect(PasswordStrength.weak.displayName, equals('Weak'));
      expect(PasswordStrength.medium.displayName, equals('Medium'));
      expect(PasswordStrength.strong.displayName, equals('Strong'));
    });

    test('should provide correct colors', () {
      expect(PasswordStrength.veryWeak.color, equals('#F44336'));
      expect(PasswordStrength.weak.color, equals('#FF9800'));
      expect(PasswordStrength.medium.color, equals('#FFC107'));
      expect(PasswordStrength.strong.color, equals('#4CAF50'));
    });
  });
}
