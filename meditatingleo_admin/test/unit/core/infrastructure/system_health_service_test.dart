import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:meditatingleo_admin/core/infrastructure/system_health_service.dart';
import 'package:meditatingleo_admin/core/infrastructure/models/system_health_status.dart';

import 'system_health_service_test.mocks.dart';

@GenerateMocks([Connectivity, SupabaseClient])
void main() {
  group('SystemHealthService', () {
    late SystemHealthService healthService;
    late MockConnectivity mockConnectivity;
    late MockSupabaseClient mockSupabaseClient;

    setUp(() {
      mockConnectivity = MockConnectivity();
      mockSupabaseClient = MockSupabaseClient();

      healthService = SystemHealthService(
        mockConnectivity,
        mockSupabaseClient,
      );
    });

    group('checkNetworkConnectivity', () {
      test('should return true when connected to wifi', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => [ConnectivityResult.wifi]);

        // Act
        final result = await healthService.checkNetworkConnectivity();

        // Assert
        expect(result, isTrue);
      });

      test('should return false when not connected', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenAnswer((_) async => [ConnectivityResult.none]);

        // Act
        final result = await healthService.checkNetworkConnectivity();

        // Assert
        expect(result, isFalse);
      });

      test('should handle connectivity check errors', () async {
        // Arrange
        when(mockConnectivity.checkConnectivity())
            .thenThrow(Exception('Connectivity error'));

        // Act
        final result = await healthService.checkNetworkConnectivity();

        // Assert
        expect(result, isFalse);
      });
    });

    group('startHealthMonitoring', () {
      test('should start periodic health monitoring', () {
        // Act
        healthService.startHealthMonitoring();

        // Assert
        expect(healthService.isMonitoring, isTrue);
      });
    });

    group('stopHealthMonitoring', () {
      test('should stop health monitoring', () {
        // Arrange
        healthService.startHealthMonitoring();

        // Act
        healthService.stopHealthMonitoring();

        // Assert
        expect(healthService.isMonitoring, isFalse);
      });
    });

    group('dispose', () {
      test('should dispose health service', () {
        // Arrange
        healthService.startHealthMonitoring();

        // Act
        healthService.dispose();

        // Assert
        expect(healthService.isMonitoring, isFalse);
      });
    });
  });
}
