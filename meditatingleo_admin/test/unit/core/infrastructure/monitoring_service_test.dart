import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'package:meditatingleo_admin/core/infrastructure/admin_monitoring_service.dart';

import 'monitoring_service_test.mocks.dart';

@GenerateMocks([Hub])
void main() {
  group('AdminMonitoringService', () {
    late AdminMonitoringService monitoringService;
    late MockHub mockHub;

    setUp(() {
      mockHub = MockHub();
      monitoringService = AdminMonitoringService(mockHub);
    });

    group('reportError', () {
      test('should report error with context', () async {
        // Arrange
        final error = Exception('Test error');
        final stackTrace = StackTrace.current;
        const context = {'user_id': 'admin_123', 'action': 'content_creation'};

        when(mockHub.captureException(
          any,
          stackTrace: anyNamed('stackTrace'),
          withScope: anyNamed('withScope'),
        )).thenAnswer((_) async => SentryId.newId());

        // Act
        await monitoringService.reportError(error, stackTrace, context);

        // Assert
        verify(mockHub.captureException(
          error,
          stackTrace: stackTrace,
          withScope: anyNamed('withScope'),
        )).called(1);
      });

      test('should report error without context', () async {
        // Arrange
        final error = Exception('Test error');
        final stackTrace = StackTrace.current;

        when(mockHub.captureException(
          any,
          stackTrace: anyNamed('stackTrace'),
          withScope: anyNamed('withScope'),
        )).thenAnswer((_) async => SentryId.newId());

        // Act
        await monitoringService.reportError(error, stackTrace);

        // Assert
        verify(mockHub.captureException(
          error,
          stackTrace: stackTrace,
          withScope: anyNamed('withScope'),
        )).called(1);
      });

      test('should handle reporting errors gracefully', () async {
        // Arrange
        final error = Exception('Test error');
        final stackTrace = StackTrace.current;
        when(mockHub.captureException(any, stackTrace: anyNamed('stackTrace')))
            .thenThrow(Exception('Sentry error'));

        // Act & Assert
        expect(
          () => monitoringService.reportError(error, stackTrace),
          returnsNormally,
        );
      });
    });

    group('reportMessage', () {
      test('should report message with level and context', () async {
        // Arrange
        const message = 'Admin action performed';
        const level = SentryLevel.info;
        const context = {'action': 'user_created', 'admin_id': 'admin_123'};

        when(mockHub.captureMessage(
          any,
          level: anyNamed('level'),
          withScope: anyNamed('withScope'),
        )).thenAnswer((_) async => SentryId.newId());

        // Act
        await monitoringService.reportMessage(message, level, context);

        // Assert
        verify(mockHub.captureMessage(
          message,
          level: level,
          withScope: anyNamed('withScope'),
        )).called(1);
      });

      test('should report message with default level', () async {
        // Arrange
        const message = 'Default message';

        when(mockHub.captureMessage(
          any,
          level: anyNamed('level'),
          withScope: anyNamed('withScope'),
        )).thenAnswer((_) async => SentryId.newId());

        // Act
        await monitoringService.reportMessage(message);

        // Assert
        verify(mockHub.captureMessage(
          message,
          level: SentryLevel.info,
          withScope: anyNamed('withScope'),
        )).called(1);
      });
    });

    group('addBreadcrumb', () {
      test('should add breadcrumb with category and data', () {
        // Arrange
        const message = 'User navigation';
        const category = 'navigation';
        const data = {'page': 'dashboard', 'user_id': 'admin_123'};

        // Act
        monitoringService.addBreadcrumb(message, category, data);

        // Assert
        verify(mockHub.addBreadcrumb(any)).called(1);
      });

      test('should add breadcrumb without data', () {
        // Arrange
        const message = 'Simple breadcrumb';
        const category = 'ui';

        // Act
        monitoringService.addBreadcrumb(message, category);

        // Assert
        verify(mockHub.addBreadcrumb(any)).called(1);
      });
    });

    group('setUserContext', () {
      test('should set user context', () {
        // Arrange
        const userId = 'admin_123';
        const email = '<EMAIL>';
        const role = 'system_admin';

        // Act
        monitoringService.setUserContext(userId, email, role);

        // Assert
        verify(mockHub.configureScope(any)).called(1);
      });

      test('should set user context with minimal data', () {
        // Arrange
        const userId = 'admin_123';

        // Act
        monitoringService.setUserContext(userId);

        // Assert
        verify(mockHub.configureScope(any)).called(1);
      });
    });

    group('setTag', () {
      test('should set tag', () {
        // Arrange
        const key = 'environment';
        const value = 'production';

        // Act
        monitoringService.setTag(key, value);

        // Assert
        verify(mockHub.configureScope(any)).called(1);
      });
    });

    group('setExtra', () {
      test('should set extra data', () {
        // Arrange
        const key = 'request_id';
        const value = 'req_123456';

        // Act
        monitoringService.setExtra(key, value);

        // Assert
        verify(mockHub.configureScope(any)).called(1);
      });
    });

    group('startTransaction', () {
      test('should start transaction', () {
        // Arrange
        const name = 'content_creation';
        const operation = 'admin_action';

        when(mockHub.startTransaction(any, any))
            .thenReturn(MockISentrySpan());

        // Act
        final transaction = monitoringService.startTransaction(name, operation);

        // Assert
        expect(transaction, isNotNull);
        verify(mockHub.startTransaction(name, operation)).called(1);
      });
    });
  });
}

class MockISentrySpan extends Mock implements ISentrySpan {}
