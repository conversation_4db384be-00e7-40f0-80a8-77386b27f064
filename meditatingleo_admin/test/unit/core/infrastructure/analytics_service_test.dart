import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';

import 'package:meditatingleo_admin/core/infrastructure/admin_analytics_service.dart';
import 'package:meditatingleo_admin/core/infrastructure/models/analytics_event.dart';

import 'analytics_service_test.mocks.dart';

@GenerateMocks([Mixpanel, People])
void main() {
  group('AdminAnalyticsService', () {
    late AdminAnalyticsService analyticsService;
    late MockMixpanel mockMixpanel;
    late MockPeople mockPeople;

    setUp(() {
      mockMixpanel = MockMixpanel();
      mockPeople = MockPeople();
      when(mockMixpanel.getPeople()).thenReturn(mockPeople);
      analyticsService = AdminAnalyticsService(mockMixpanel);
    });

    group('trackEvent', () {
      test('should track analytics event with properties', () async {
        // Arrange
        const event = AnalyticsEvent(
          name: 'admin_login',
          properties: {'user_role': 'admin'},
        );

        // Act
        await analyticsService.trackEvent(event);

        // Assert
        final captured = verify(mockMixpanel.track('admin_login',
                properties: captureAnyNamed('properties')))
            .captured;
        expect(captured.length, 1);
        final properties = captured[0] as Map<String, dynamic>;
        expect(properties['user_role'], 'admin');
        expect(properties.containsKey('timestamp'), isTrue);
      });

      test('should track event without properties', () async {
        // Arrange
        const event = AnalyticsEvent(name: 'page_view');

        // Act
        await analyticsService.trackEvent(event);

        // Assert
        final captured = verify(mockMixpanel.track('page_view',
                properties: captureAnyNamed('properties')))
            .captured;
        expect(captured.length, 1);
        final properties = captured[0] as Map<String, dynamic>;
        expect(properties.containsKey('timestamp'), isTrue);
      });

      test('should handle tracking errors gracefully', () async {
        // Arrange
        const event = AnalyticsEvent(name: 'error_event');
        when(mockMixpanel.track(any, properties: anyNamed('properties')))
            .thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => analyticsService.trackEvent(event),
          returnsNormally,
        );
      });
    });

    group('setUserProperties', () {
      test('should set user properties', () async {
        // Arrange
        const properties = {
          'user_id': 'admin_123',
          'role': 'system_admin',
          'department': 'IT',
        };

        // Act
        await analyticsService.setUserProperties(properties);

        // Assert
        verify(mockPeople.set('user_id', 'admin_123')).called(1);
        verify(mockPeople.set('role', 'system_admin')).called(1);
        verify(mockPeople.set('department', 'IT')).called(1);
      });

      test('should handle set user properties errors gracefully', () async {
        // Arrange
        const properties = {'user_id': 'admin_123'};
        when(mockPeople.set('user_id', any)).thenThrow(Exception('API error'));

        // Act & Assert
        expect(
          () => analyticsService.setUserProperties(properties),
          returnsNormally,
        );
      });
    });

    group('identifyUser', () {
      test('should identify user with distinct ID', () async {
        // Arrange
        const userId = 'admin_123';

        // Act
        await analyticsService.identifyUser(userId);

        // Assert
        verify(mockMixpanel.identify(userId)).called(1);
      });

      test('should handle identify errors gracefully', () async {
        // Arrange
        const userId = 'admin_123';
        when(mockMixpanel.identify(any)).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => analyticsService.identifyUser(userId),
          returnsNormally,
        );
      });
    });

    group('flush', () {
      test('should flush pending events', () async {
        // Act
        await analyticsService.flush();

        // Assert
        verify(mockMixpanel.flush()).called(1);
      });

      test('should handle flush errors gracefully', () async {
        // Arrange
        when(mockMixpanel.flush()).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => analyticsService.flush(),
          returnsNormally,
        );
      });
    });

    group('reset', () {
      test('should reset analytics state', () async {
        // Act
        await analyticsService.reset();

        // Assert
        verify(mockMixpanel.reset()).called(1);
      });

      test('should handle reset errors gracefully', () async {
        // Arrange
        when(mockMixpanel.reset()).thenThrow(Exception('API error'));

        // Act & Assert
        expect(
          () => analyticsService.reset(),
          returnsNormally,
        );
      });
    });

    group('dispose', () {
      test('should dispose analytics service', () async {
        // Act
        await analyticsService.dispose();

        // Assert
        verify(mockMixpanel.flush()).called(1);
      });
    });
  });
}
