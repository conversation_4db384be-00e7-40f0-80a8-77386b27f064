name: meditating<PERSON>_webapp
description: "ClarityByMeditatingLeo Web Application - Desktop-optimized journaling experience"
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.5.3
  flutter: ^3.24.0

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1

  # Backend & Database
  supabase_flutter: ^2.8.0

  # Navigation
  go_router: ^14.6.2

  # Data Models
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0

  # Web-Specific
  url_strategy: ^0.3.0

  # UI & Utilities
  intl: ^0.19.0

  # Environment Configuration
  flutter_dotenv: ^5.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

  # Code Generation
  build_runner: ^2.4.13
  riverpod_generator: ^2.6.2
  freezed: ^2.5.7
  json_serializable: ^6.8.0

  # Testing
  mockito: ^5.4.4

flutter:
  uses-material-design: true

  assets:
    - .env
