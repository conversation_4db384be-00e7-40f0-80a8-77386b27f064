import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'app/app.dart';
import 'core/config/env_config.dart';
import 'core/constants/app_constants.dart';

/// Main entry point for the MeditatingLeo Web Application.
///
/// This function initializes the Flutter app with necessary services
/// and providers, then runs the main application widget.
Future<void> main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize environment configuration
  await EnvConfig.initialize();

  // Initialize Supabase with environment configuration
  await Supabase.initialize(
    url: EnvConfig.supabaseUrl,
    anonKey: EnvConfig.supabaseAnonKey,
  );

  // Set up global error handling
  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    if (AppConstants.isDebugMode) {
      // In debug mode, print the error to console
      debugPrint('Flutter Error: ${details.exception}');
      debugPrint('Stack trace: ${details.stack}');
    }
  };

  // Run the app with Riverpod provider scope
  runApp(
    const ProviderScope(
      child: WebApp(),
    ),
  );
}
