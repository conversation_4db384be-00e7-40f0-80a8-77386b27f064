import '../config/env_config.dart';

/// UI-specific constants for responsive design and layout.
///
/// This file contains breakpoints, spacing, sizing, and other UI-related
/// constants used throughout the web application.
class UIConstants {
  // Private constructor to prevent instantiation
  UIConstants._();

  /// Responsive breakpoints for different screen sizes (configurable via environment)
  static double get mobileBreakpoint => EnvConfig.responsiveBreakpointMobile;
  static double get tabletBreakpoint => EnvConfig.responsiveBreakpointTablet;
  static double get desktopBreakpoint => EnvConfig.responsiveBreakpointDesktop;

  /// Standard spacing values following Material Design 3 guidelines
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing48 = 48.0;
  static const double spacing64 = 64.0;

  /// Border radius values for consistent rounded corners
  static const double borderRadius4 = 4.0;
  static const double borderRadius8 = 8.0;
  static const double borderRadius12 = 12.0;
  static const double borderRadius16 = 16.0;
  static const double borderRadius24 = 24.0;

  /// Elevation values for Material Design 3 surfaces
  static const double elevation0 = 0.0;
  static const double elevation1 = 1.0;
  static const double elevation2 = 2.0;
  static const double elevation3 = 3.0;
  static const double elevation4 = 4.0;
  static const double elevation6 = 6.0;

  /// Icon sizes for different contexts
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;

  /// Button heights for different button types
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeightMedium = 40.0;
  static const double buttonHeightLarge = 48.0;

  /// Maximum width for content containers
  static const double maxContentWidth = 1200.0;
  static const double maxFormWidth = 600.0;
  static const double maxCardWidth = 400.0;

  /// Sidebar and navigation dimensions
  static const double sidebarWidth = 280.0;
  static const double sidebarWidthCollapsed = 72.0;
  static const double navigationBarHeight = 64.0;

  /// Z-index values for layering
  static const int zIndexBase = 0;
  static const int zIndexDropdown = 1000;
  static const int zIndexModal = 2000;
  static const int zIndexTooltip = 3000;
}
