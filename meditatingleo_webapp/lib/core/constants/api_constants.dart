import '../config/env_config.dart';

/// API and backend-related constants for the MeditatingLeo Web Application.
///
/// This file contains all the API endpoints, configuration values, and
/// backend-related constants used throughout the web application.
class ApiConstants {
  // Private constructor to prevent instantiation
  ApiConstants._();

  /// Supabase configuration from environment
  static String get supabaseUrl => EnvConfig.supabaseUrl;
  static String get supabaseAnonKey => EnvConfig.supabaseAnonKey;

  /// API endpoints for different services
  static const String authEndpoint = '/auth/v1';
  static const String restEndpoint = '/rest/v1';
  static const String realtimeEndpoint = '/realtime/v1';
  static const String storageEndpoint = '/storage/v1';

  /// Database table names
  static const String usersTable = 'users';
  static const String journeyTable = 'journeys';
  static const String promptsTable = 'prompts';
  static const String entriesTable = 'journal_entries';
  static const String goalsTable = 'goals';
  static const String habitsTable = 'habits';
  static const String sessionsTable = 'focus_sessions';

  /// API request timeouts
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration uploadTimeout = Duration(minutes: 5);
  static const Duration downloadTimeout = Duration(minutes: 2);

  /// Pagination constants
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const int minPageSize = 5;

  /// Cache configuration
  static const Duration cacheExpiration = Duration(hours: 1);
  static const Duration offlineCacheExpiration = Duration(days: 7);
  static const int maxCacheSize = 50; // Number of items

  /// Real-time subscription channels
  static const String userChannel = 'user_updates';
  static const String journeyChannel = 'journey_updates';
  static const String entryChannel = 'entry_updates';

  /// File upload constraints
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/webp',
  ];
  static const List<String> allowedDocumentTypes = [
    'application/pdf',
    'text/plain',
    'text/markdown',
  ];

  /// Rate limiting
  static const int maxRequestsPerMinute = 60;
  static const int maxRequestsPerHour = 1000;

  /// Error codes
  static const String errorCodeUnauthorized = 'UNAUTHORIZED';
  static const String errorCodeForbidden = 'FORBIDDEN';
  static const String errorCodeNotFound = 'NOT_FOUND';
  static const String errorCodeValidation = 'VALIDATION_ERROR';
  static const String errorCodeServerError = 'SERVER_ERROR';
  static const String errorCodeNetworkError = 'NETWORK_ERROR';
  static const String errorCodeTimeout = 'TIMEOUT';

  /// Success codes
  static const String successCodeCreated = 'CREATED';
  static const String successCodeUpdated = 'UPDATED';
  static const String successCodeDeleted = 'DELETED';

  /// Headers
  static const String authorizationHeader = 'Authorization';
  static const String contentTypeHeader = 'Content-Type';
  static const String acceptHeader = 'Accept';
  static const String userAgentHeader = 'User-Agent';

  /// Content types
  static const String jsonContentType = 'application/json';
  static const String formContentType = 'application/x-www-form-urlencoded';
  static const String multipartContentType = 'multipart/form-data';

  /// User agent
  static const String userAgent = 'ClarityByMeditatingLeo-Web/1.0.0';

  /// Feature flags from backend
  static const String featureFlagRealtime = 'realtime_enabled';
  static const String featureFlagOfflineMode = 'offline_mode_enabled';
  static const String featureFlagAnalytics = 'analytics_enabled';
  static const String featureFlagNotifications = 'notifications_enabled';
}
