import '../config/env_config.dart';

/// Application-wide constants for the MeditatingLeo Web Application.
///
/// This file contains all the constant values used throughout the web app
/// including app metadata, configuration values, and feature flags.
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  /// Application name displayed in the browser title (configurable)
  static String get appName => EnvConfig.webAppName;

  /// Application version (configurable)
  static String get appVersion => EnvConfig.webAppVersion;

  /// Application description for SEO and metadata (configurable)
  static String get appDescription => EnvConfig.pwaDescription;

  /// Default page title format
  static String get pageTitleFormat => '$appName - %s';

  /// Maximum content width for desktop layouts
  static const double maxContentWidth = 1200.0;

  /// Default animation duration for UI transitions
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);

  /// Default debounce duration for search and input
  static const Duration defaultDebounceDuration = Duration(milliseconds: 500);

  /// Feature flags for progressive enhancement (configurable)
  static bool get enableOfflineMode => EnvConfig.enableOfflineMode;
  static bool get enablePushNotifications => EnvConfig.enablePushNotifications;
  static bool get enableAnalytics => EnvConfig.enableAnalytics;

  /// Environment configuration (configurable)
  static String get environment => EnvConfig.flutterEnv;

  /// Debug mode flag (configurable)
  static bool get isDebugMode => EnvConfig.debugMode;
}
