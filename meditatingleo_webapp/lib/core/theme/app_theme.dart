import 'package:flutter/material.dart';

import 'color_scheme.dart';
import 'text_theme.dart';
import '../constants/ui_constants.dart';

/// Main theme configuration for the MeditatingLeo Web Application.
///
/// This class provides Material Design 3 themes with custom branding,
/// web-optimized components, and responsive design considerations.
class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();

  /// Light theme configuration
  static ThemeData get lightTheme {
    final colorScheme = AppColorScheme.lightColorScheme;
    final textTheme = AppTextTheme.createTextTheme(colorScheme);

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: textTheme,

      // App bar theme
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: UIConstants.elevation0,
        centerTitle: false,
        titleTextStyle: textTheme.titleLarge,
      ),

      // Card theme
      cardTheme: CardThemeData(
        elevation: UIConstants.elevation1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.borderRadius12),
        ),
      ),

      // Elevated button theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: UIConstants.elevation1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UIConstants.borderRadius8),
          ),
          minimumSize: const Size(0, UIConstants.buttonHeightMedium),
        ),
      ),

      // Filled button theme (Material Design 3)
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UIConstants.borderRadius8),
          ),
          minimumSize: const Size(0, UIConstants.buttonHeightMedium),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(UIConstants.borderRadius8),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: UIConstants.spacing16,
          vertical: UIConstants.spacing12,
        ),
      ),

      // Navigation bar theme
      navigationBarTheme: NavigationBarThemeData(
        height: UIConstants.navigationBarHeight,
        elevation: UIConstants.elevation0,
        backgroundColor: colorScheme.surface,
      ),

      // Divider theme
      dividerTheme: DividerThemeData(
        color: colorScheme.outline,
        thickness: 1,
        space: 1,
      ),
    );
  }

  /// Dark theme configuration
  static ThemeData get darkTheme {
    final colorScheme = AppColorScheme.darkColorScheme;
    final textTheme = AppTextTheme.createTextTheme(colorScheme);

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: textTheme,

      // App bar theme
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: UIConstants.elevation0,
        centerTitle: false,
        titleTextStyle: textTheme.titleLarge,
      ),

      // Card theme
      cardTheme: CardThemeData(
        elevation: UIConstants.elevation1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(UIConstants.borderRadius12),
        ),
      ),

      // Elevated button theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: UIConstants.elevation1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UIConstants.borderRadius8),
          ),
          minimumSize: const Size(0, UIConstants.buttonHeightMedium),
        ),
      ),

      // Filled button theme (Material Design 3)
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UIConstants.borderRadius8),
          ),
          minimumSize: const Size(0, UIConstants.buttonHeightMedium),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(UIConstants.borderRadius8),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: UIConstants.spacing16,
          vertical: UIConstants.spacing12,
        ),
      ),

      // Navigation bar theme
      navigationBarTheme: NavigationBarThemeData(
        height: UIConstants.navigationBarHeight,
        elevation: UIConstants.elevation0,
        backgroundColor: colorScheme.surface,
      ),

      // Divider theme
      dividerTheme: DividerThemeData(
        color: colorScheme.outline,
        thickness: 1,
        space: 1,
      ),
    );
  }
}
