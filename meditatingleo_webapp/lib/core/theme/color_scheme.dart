import 'package:flutter/material.dart';

/// Custom color schemes for the MeditatingLeo Web Application.
///
/// This class provides Material Design 3 color schemes with custom
/// brand colors and semantic color definitions.
class AppColorScheme {
  // Private constructor to prevent instantiation
  AppColorScheme._();

  /// Primary brand color - calming blue for mindfulness
  static const Color _primaryColor = Color(0xFF2196F3);

  /// Secondary brand color - warm accent
  static const Color _secondaryColor = Color(0xFF4CAF50);

  /// Error color following Material Design 3
  static const Color _errorColor = Color(0xFFE53935);

  /// Light color scheme for the application
  static final ColorScheme lightColorScheme = ColorScheme.fromSeed(
    seedColor: _primaryColor,
    brightness: Brightness.light,
    secondary: _secondaryColor,
    error: _errorColor,
  );

  /// Dark color scheme for the application
  static final ColorScheme darkColorScheme = ColorScheme.fromSeed(
    seedColor: _primaryColor,
    brightness: Brightness.dark,
    secondary: _secondaryColor,
    error: _errorColor,
  );

  /// Surface colors with modern opacity values using Color.withValues()
  static Color surfaceVariant(ColorScheme colorScheme) {
    return colorScheme.surface.withValues(alpha: 0.8);
  }

  /// Overlay colors for modals and dialogs
  static Color overlay(ColorScheme colorScheme) {
    return colorScheme.onSurface.withValues(alpha: 0.12);
  }

  /// Disabled colors for inactive elements
  static Color disabled(ColorScheme colorScheme) {
    return colorScheme.onSurface.withValues(alpha: 0.38);
  }

  /// Focus colors for interactive elements
  static Color focus(ColorScheme colorScheme) {
    return colorScheme.primary.withValues(alpha: 0.12);
  }

  /// Hover colors for web interactions
  static Color hover(ColorScheme colorScheme) {
    return colorScheme.primary.withValues(alpha: 0.08);
  }

  /// Success color for positive feedback
  static const Color success = Color(0xFF4CAF50);

  /// Warning color for cautionary feedback
  static const Color warning = Color(0xFFFF9800);

  /// Info color for informational feedback
  static const Color info = Color(0xFF2196F3);
}
