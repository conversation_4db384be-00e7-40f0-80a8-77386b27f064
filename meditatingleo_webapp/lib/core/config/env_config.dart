import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Environment configuration service for the MeditatingLeo Web Application.
///
/// This service provides access to environment variables loaded from .env files
/// with type-safe getters and default values for all configuration options.
class EnvConfig {
  // Private constructor to prevent instantiation
  EnvConfig._();

  /// Initialize the environment configuration
  static Future<void> initialize() async {
    await dotenv.load(fileName: '.env');
  }

  /// Supabase Configuration
  static String get supabaseUrl => 
      dotenv.env['SUPABASE_URL'] ?? 'https://placeholder.supabase.co';
  
  static String get supabaseAnonKey => 
      dotenv.env['SUPABASE_ANON_KEY'] ?? 'placeholder-anon-key';

  /// Web Application Configuration
  static String get webAppUrl => 
      dotenv.env['WEB_APP_URL'] ?? 'http://localhost:8080';
  
  static String get webAppName => 
      dotenv.env['WEB_APP_NAME'] ?? 'ClarityByMeditatingLeo';
  
  static String get webAppVersion => 
      dotenv.env['WEB_APP_VERSION'] ?? '1.0.0';

  /// Development Settings
  static String get flutterEnv => 
      dotenv.env['FLUTTER_ENV'] ?? 'development';
  
  static bool get debugMode => 
      dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true';
  
  static String get logLevel => 
      dotenv.env['LOG_LEVEL'] ?? 'debug';

  /// PWA Configuration
  static String get pwaName => 
      dotenv.env['PWA_NAME'] ?? 'Clarity';
  
  static String get pwaShortName => 
      dotenv.env['PWA_SHORT_NAME'] ?? 'Clarity';
  
  static String get pwaDescription => 
      dotenv.env['PWA_DESCRIPTION'] ?? 
      'Desktop-optimized journaling experience for clarity and mindfulness';
  
  static String get pwaThemeColor => 
      dotenv.env['PWA_THEME_COLOR'] ?? '#2196F3';
  
  static String get pwaBackgroundColor => 
      dotenv.env['PWA_BACKGROUND_COLOR'] ?? '#2196F3';

  /// Performance Settings
  static int get cacheTtl => 
      int.tryParse(dotenv.env['CACHE_TTL'] ?? '') ?? 3600;
  
  static String get serviceWorkerCacheVersion => 
      dotenv.env['SERVICE_WORKER_CACHE_VERSION'] ?? 'v1.0.0';
  
  static int get offlineCacheSize => 
      int.tryParse(dotenv.env['OFFLINE_CACHE_SIZE'] ?? '') ?? 50;

  /// Feature Flags
  static bool get enableOfflineMode => 
      dotenv.env['ENABLE_OFFLINE_MODE']?.toLowerCase() == 'true';
  
  static bool get enablePushNotifications => 
      dotenv.env['ENABLE_PUSH_NOTIFICATIONS']?.toLowerCase() == 'true';
  
  static bool get enableAnalytics => 
      dotenv.env['ENABLE_ANALYTICS']?.toLowerCase() == 'true';
  
  static bool get enableRealTimeSync => 
      dotenv.env['ENABLE_REAL_TIME_SYNC']?.toLowerCase() == 'true';
  
  static bool get enableVoiceToText => 
      dotenv.env['ENABLE_VOICE_TO_TEXT']?.toLowerCase() == 'true';

  /// UI Configuration
  static String get defaultTheme => 
      dotenv.env['DEFAULT_THEME'] ?? 'light';
  
  static bool get enableDarkMode => 
      dotenv.env['ENABLE_DARK_MODE']?.toLowerCase() == 'true';
  
  static double get responsiveBreakpointMobile => 
      double.tryParse(dotenv.env['RESPONSIVE_BREAKPOINT_MOBILE'] ?? '') ?? 600.0;
  
  static double get responsiveBreakpointTablet => 
      double.tryParse(dotenv.env['RESPONSIVE_BREAKPOINT_TABLET'] ?? '') ?? 900.0;
  
  static double get responsiveBreakpointDesktop => 
      double.tryParse(dotenv.env['RESPONSIVE_BREAKPOINT_DESKTOP'] ?? '') ?? 1200.0;

  /// Content Configuration
  static int get maxJournalEntryLength => 
      int.tryParse(dotenv.env['MAX_JOURNAL_ENTRY_LENGTH'] ?? '') ?? 10000;
  
  static int get autoSaveInterval => 
      int.tryParse(dotenv.env['AUTO_SAVE_INTERVAL'] ?? '') ?? 30000;
  
  static bool get richTextEditor => 
      dotenv.env['RICH_TEXT_EDITOR']?.toLowerCase() == 'true';

  /// Localization
  static String get defaultLocale => 
      dotenv.env['DEFAULT_LOCALE'] ?? 'en_US';
  
  static List<String> get supportedLocales => 
      dotenv.env['SUPPORTED_LOCALES']?.split(',') ?? ['en_US'];

  /// Rate Limiting
  static int get apiRateLimit => 
      int.tryParse(dotenv.env['API_RATE_LIMIT'] ?? '') ?? 1000;
  
  static int get userRateLimit => 
      int.tryParse(dotenv.env['USER_RATE_LIMIT'] ?? '') ?? 100;

  /// Security
  static bool get enableCors => 
      dotenv.env['ENABLE_CORS']?.toLowerCase() == 'true';
  
  static List<String> get corsAllowedOrigins => 
      dotenv.env['CORS_ALLOWED_ORIGINS']?.split(',') ?? ['http://localhost:8080'];
  
  static bool get enableCsp => 
      dotenv.env['ENABLE_CSP']?.toLowerCase() == 'true';
  
  static String get cspPolicy => 
      dotenv.env['CSP_POLICY'] ?? "default-src 'self'";

  /// Analytics & Monitoring (Optional)
  static String? get sentryDsn => dotenv.env['SENTRY_DSN'];
  static String? get mixpanelToken => dotenv.env['MIXPANEL_TOKEN'];
  static String? get googleAnalyticsId => dotenv.env['GOOGLE_ANALYTICS_ID'];

  /// Build Configuration
  static String get buildMode => 
      dotenv.env['BUILD_MODE'] ?? 'debug';
  
  static String get targetPlatform => 
      dotenv.env['TARGET_PLATFORM'] ?? 'web';
  
  static String get webRenderer => 
      dotenv.env['WEB_RENDERER'] ?? 'canvaskit';

  /// Development Tools
  static bool get enableFlutterInspector => 
      dotenv.env['ENABLE_FLUTTER_INSPECTOR']?.toLowerCase() == 'true';
  
  static bool get enablePerformanceOverlay => 
      dotenv.env['ENABLE_PERFORMANCE_OVERLAY']?.toLowerCase() == 'true';
  
  static bool get enableDebugBanner => 
      dotenv.env['ENABLE_DEBUG_BANNER']?.toLowerCase() == 'true';

  /// Branding
  static String get appLogoUrl => 
      dotenv.env['APP_LOGO_URL'] ?? 'assets/images/logo.png';
  
  static String get companyName => 
      dotenv.env['COMPANY_NAME'] ?? 'MeditatingLeo';
  
  static String get supportEmail => 
      dotenv.env['SUPPORT_EMAIL'] ?? '<EMAIL>';
  
  static String? get supportUrl => dotenv.env['SUPPORT_URL'];

  /// Maintenance Mode
  static bool get maintenanceMode => 
      dotenv.env['MAINTENANCE_MODE']?.toLowerCase() == 'true';
  
  static String get maintenanceMessage => 
      dotenv.env['MAINTENANCE_MESSAGE'] ?? 
      'System is under maintenance. Please try again later.';

  /// Privacy & Compliance
  static bool get gdprCompliance => 
      dotenv.env['GDPR_COMPLIANCE']?.toLowerCase() == 'true';
  
  static bool get cookieConsentRequired => 
      dotenv.env['COOKIE_CONSENT_REQUIRED']?.toLowerCase() == 'true';
  
  static int get dataRetentionDays => 
      int.tryParse(dotenv.env['DATA_RETENTION_DAYS'] ?? '') ?? 365;

  /// Accessibility
  static bool get enableHighContrast => 
      dotenv.env['ENABLE_HIGH_CONTRAST']?.toLowerCase() == 'true';
  
  static bool get enableScreenReaderSupport => 
      dotenv.env['ENABLE_SCREEN_READER_SUPPORT']?.toLowerCase() == 'true';
  
  static bool get enableKeyboardNavigation => 
      dotenv.env['ENABLE_KEYBOARD_NAVIGATION']?.toLowerCase() == 'true';
  
  static bool get fontSizeScaling => 
      dotenv.env['FONT_SIZE_SCALING']?.toLowerCase() == 'true';

  /// User Experience
  static bool get onboardingEnabled => 
      dotenv.env['ONBOARDING_ENABLED']?.toLowerCase() == 'true';
  
  static bool get tutorialEnabled => 
      dotenv.env['TUTORIAL_ENABLED']?.toLowerCase() == 'true';
  
  static bool get helpSystemEnabled => 
      dotenv.env['HELP_SYSTEM_ENABLED']?.toLowerCase() == 'true';
  
  static bool get feedbackCollection => 
      dotenv.env['FEEDBACK_COLLECTION']?.toLowerCase() == 'true';

  /// Web App Manifest
  static String get manifestStartUrl => 
      dotenv.env['MANIFEST_START_URL'] ?? '/';
  
  static String get manifestDisplay => 
      dotenv.env['MANIFEST_DISPLAY'] ?? 'standalone';
  
  static String get manifestOrientation => 
      dotenv.env['MANIFEST_ORIENTATION'] ?? 'any';
  
  static List<String> get manifestCategories => 
      dotenv.env['MANIFEST_CATEGORIES']?.split(',') ?? 
      ['productivity', 'lifestyle', 'health'];

  /// Utility method to check if running in production
  static bool get isProduction => flutterEnv == 'production';
  
  /// Utility method to check if running in development
  static bool get isDevelopment => flutterEnv == 'development';
  
  /// Utility method to check if running in staging
  static bool get isStaging => flutterEnv == 'staging';
}
