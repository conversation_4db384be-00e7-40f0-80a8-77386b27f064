import 'dart:io' show Platform;

import 'package:flutter/foundation.dart' show kIsWeb;

/// Utility class for platform detection and platform-specific functionality.
///
/// This class provides methods to detect the current platform and adapt
/// behavior accordingly for web, mobile, and desktop environments.
class PlatformUtils {
  // Private constructor to prevent instantiation
  PlatformUtils._();

  /// Returns true if running on web platform
  static bool get isWeb => kIsWeb;

  /// Returns true if running on mobile platforms (iOS or Android)
  static bool get isMobile => !kIsWeb && (Platform.isIOS || Platform.isAndroid);

  /// Returns true if running on desktop platforms
  static bool get isDesktop => 
      !kIsWeb && (Platform.isWindows || Platform.isMacOS || Platform.isLinux);

  /// Returns true if running on iOS
  static bool get isIOS => !kIsWeb && Platform.isIOS;

  /// Returns true if running on Android
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;

  /// Returns true if running on Windows
  static bool get isWindows => !kIsWeb && Platform.isWindows;

  /// Returns true if running on macOS
  static bool get isMacOS => !kIsWeb && Platform.isMacOS;

  /// Returns true if running on Linux
  static bool get isLinux => !kIsWeb && Platform.isLinux;

  /// Returns the platform name as a string
  static String get platformName {
    if (isWeb) return 'Web';
    if (isIOS) return 'iOS';
    if (isAndroid) return 'Android';
    if (isWindows) return 'Windows';
    if (isMacOS) return 'macOS';
    if (isLinux) return 'Linux';
    return 'Unknown';
  }

  /// Returns true if the platform supports biometric authentication
  static bool get supportsBiometrics => isMobile;

  /// Returns true if the platform supports push notifications
  static bool get supportsPushNotifications => isMobile || isWeb;

  /// Returns true if the platform supports file system access
  static bool get supportsFileSystem => !isWeb;

  /// Returns true if the platform supports background processing
  static bool get supportsBackgroundProcessing => isMobile;
}
