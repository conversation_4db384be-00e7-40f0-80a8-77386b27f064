import 'package:flutter/material.dart';

import '../../core/constants/ui_constants.dart';

/// A responsive layout widget that adapts to different screen sizes.
///
/// This widget automatically selects the appropriate layout based on
/// screen width using predefined breakpoints. It supports mobile,
/// tablet, and desktop layouts with graceful fallbacks.
///
/// Example usage:
/// ```dart
/// ResponsiveLayout(
///   mobile: MobileLayout(),
///   tablet: TabletLayout(),
///   desktop: DesktopLayout(),
/// )
/// ```
class ResponsiveLayout extends StatelessWidget {
  /// Widget to display on mobile screens (< 600px)
  final Widget mobile;

  /// Widget to display on tablet screens (600px - 900px)
  /// If not provided, falls back to mobile layout
  final Widget? tablet;

  /// Widget to display on desktop screens (> 900px)
  final Widget desktop;

  /// Creates a responsive layout widget.
  ///
  /// [mobile] and [desktop] are required. [tablet] is optional and
  /// will fall back to [mobile] if not provided.
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    required this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= UIConstants.tabletBreakpoint) {
          return desktop;
        } else if (constraints.maxWidth >= UIConstants.mobileBreakpoint) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}

/// Utility class for responsive design helpers.
///
/// Provides static methods to check screen size categories and
/// get responsive values based on current screen size.
class ResponsiveUtils {
  // Private constructor to prevent instantiation
  ResponsiveUtils._();

  /// Returns true if the current screen is mobile size
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < UIConstants.mobileBreakpoint;
  }

  /// Returns true if the current screen is tablet size
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= UIConstants.mobileBreakpoint && 
           width < UIConstants.tabletBreakpoint;
  }

  /// Returns true if the current screen is desktop size
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= UIConstants.tabletBreakpoint;
  }

  /// Returns a responsive value based on screen size
  static T responsive<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    required T desktop,
  }) {
    if (isDesktop(context)) {
      return desktop;
    } else if (isTablet(context)) {
      return tablet ?? mobile;
    } else {
      return mobile;
    }
  }

  /// Returns responsive padding based on screen size
  static EdgeInsets responsivePadding(BuildContext context) {
    return EdgeInsets.all(
      responsive(
        context,
        mobile: UIConstants.spacing16,
        tablet: UIConstants.spacing24,
        desktop: UIConstants.spacing32,
      ),
    );
  }

  /// Returns responsive horizontal padding based on screen size
  static EdgeInsets responsiveHorizontalPadding(BuildContext context) {
    return EdgeInsets.symmetric(
      horizontal: responsive(
        context,
        mobile: UIConstants.spacing16,
        tablet: UIConstants.spacing24,
        desktop: UIConstants.spacing32,
      ),
    );
  }
}
