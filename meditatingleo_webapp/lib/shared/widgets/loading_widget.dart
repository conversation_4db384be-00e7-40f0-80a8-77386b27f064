import 'package:flutter/material.dart';

import '../../core/constants/ui_constants.dart';

/// A reusable loading widget with consistent styling.
///
/// This widget displays a circular progress indicator with optional
/// message text. It follows Material Design 3 guidelines and provides
/// consistent loading states throughout the application.
///
/// Example usage:
/// ```dart
/// LoadingWidget(message: 'Loading your journal entries...')
/// ```
class LoadingWidget extends StatelessWidget {
  /// Optional message to display below the loading indicator
  final String? message;

  /// Size of the loading indicator
  final double? size;

  /// Whether to center the loading widget
  final bool centered;

  /// Creates a loading widget.
  ///
  /// [message] is optional and will display below the indicator.
  /// [size] controls the size of the circular progress indicator.
  /// [centered] determines if the widget should be centered in its parent.
  const LoadingWidget({
    super.key,
    this.message,
    this.size,
    this.centered = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final loadingContent = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size ?? UIConstants.iconSizeLarge,
          height: size ?? UIConstants.iconSizeLarge,
          child: CircularProgressIndicator(
            strokeWidth: 3.0,
            color: theme.colorScheme.primary,
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: UIConstants.spacing16),
          Text(
            message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );

    if (centered) {
      return Center(child: loadingContent);
    }

    return loadingContent;
  }
}

/// A minimal loading widget for inline use.
///
/// This widget displays a small circular progress indicator without
/// any additional text or centering. Useful for buttons or small spaces.
class InlineLoadingWidget extends StatelessWidget {
  /// Size of the loading indicator
  final double size;

  /// Color of the loading indicator
  final Color? color;

  /// Creates an inline loading widget.
  ///
  /// [size] controls the size of the indicator.
  /// [color] overrides the default theme color.
  const InlineLoadingWidget({
    super.key,
    this.size = UIConstants.iconSizeMedium,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2.0,
        color: color ?? theme.colorScheme.primary,
      ),
    );
  }
}
