import 'package:flutter/material.dart';

import '../../core/constants/ui_constants.dart';

/// A reusable error widget with consistent styling and retry functionality.
///
/// This widget displays error messages with an optional retry button.
/// It follows Material Design 3 guidelines and provides consistent
/// error states throughout the application.
///
/// Example usage:
/// ```dart
/// AppErrorWidget(
///   message: 'Failed to load data',
///   onRetry: () => loadData(),
/// )
/// ```
class AppErrorWidget extends StatelessWidget {
  /// Error message to display
  final String message;

  /// Optional callback for retry functionality
  final VoidCallback? onRetry;

  /// Optional icon to display above the message
  final IconData? icon;

  /// Whether to center the error widget
  final bool centered;

  /// Creates an error widget.
  ///
  /// [message] is required and displays the error text.
  /// [onRetry] is optional and shows a retry button when provided.
  /// [icon] is optional and displays above the message.
  /// [centered] determines if the widget should be centered in its parent.
  const AppErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.icon,
    this.centered = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final errorContent = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon ?? Icons.error_outline,
          size: UIConstants.iconSizeXLarge,
          color: theme.colorScheme.error,
        ),
        const SizedBox(height: UIConstants.spacing16),
        Text(
          message,
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        if (onRetry != null) ...[
          const SizedBox(height: UIConstants.spacing24),
          FilledButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ],
    );

    if (centered) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(UIConstants.spacing24),
          child: errorContent,
        ),
      );
    }

    return errorContent;
  }
}

/// A minimal error widget for inline use.
///
/// This widget displays a small error icon with tooltip text.
/// Useful for form fields or small spaces where a full error
/// widget would be too large.
class InlineErrorWidget extends StatelessWidget {
  /// Error message to display in tooltip
  final String message;

  /// Size of the error icon
  final double size;

  /// Color of the error icon
  final Color? color;

  /// Creates an inline error widget.
  ///
  /// [message] is required for the tooltip text.
  /// [size] controls the size of the error icon.
  /// [color] overrides the default error color.
  const InlineErrorWidget({
    super.key,
    required this.message,
    this.size = UIConstants.iconSizeMedium,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Tooltip(
      message: message,
      child: Icon(
        Icons.error_outline,
        size: size,
        color: color ?? theme.colorScheme.error,
      ),
    );
  }
}
