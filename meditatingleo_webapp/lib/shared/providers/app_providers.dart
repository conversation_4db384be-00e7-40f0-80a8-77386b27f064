import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../app/router/app_router.dart';
import '../../core/theme/app_theme.dart';

part 'app_providers.g.dart';

/// Provides the Supabase client instance for the application.
///
/// This provider initializes and maintains the Supabase client
/// used throughout the web application for backend communication.
@riverpod
SupabaseClient supabase(Ref ref) {
  return Supabase.instance.client;
}

/// Provides the GoRouter instance for navigation.
///
/// This provider configures the routing system for the web application
/// with proper route definitions and navigation handling.
@riverpod
GoRouter router(Ref ref) {
  return AppRouter.createRouter();
}

/// Provides the theme data for the application.
///
/// This provider manages the current theme (light/dark) and
/// provides the appropriate ThemeData for the MaterialApp.
@riverpod
class ThemeNotifier extends _$ThemeNotifier {
  @override
  ThemeData build() {
    // Default to light theme
    return AppTheme.lightTheme;
  }

  /// Switches to light theme
  void setLightTheme() {
    state = AppTheme.lightTheme;
  }

  /// Switches to dark theme
  void setDarkTheme() {
    state = AppTheme.darkTheme;
  }

  /// Toggles between light and dark theme
  void toggleTheme() {
    state = state.brightness == Brightness.light
        ? AppTheme.darkTheme
        : AppTheme.lightTheme;
  }
}

/// Convenience provider for accessing the current theme
@riverpod
ThemeData theme(Ref ref) {
  return ref.watch(themeNotifierProvider);
}
