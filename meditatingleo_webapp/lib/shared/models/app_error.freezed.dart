// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppError {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppErrorCopyWith<$Res> {
  factory $AppErrorCopyWith(AppError value, $Res Function(AppError) then) =
      _$AppErrorCopyWithImpl<$Res, AppError>;
}

/// @nodoc
class _$AppErrorCopyWithImpl<$Res, $Val extends AppError>
    implements $AppErrorCopyWith<$Res> {
  _$AppErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$NetworkErrorImplCopyWith<$Res> {
  factory _$$NetworkErrorImplCopyWith(
          _$NetworkErrorImpl value, $Res Function(_$NetworkErrorImpl) then) =
      __$$NetworkErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$NetworkErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$NetworkErrorImpl>
    implements _$$NetworkErrorImplCopyWith<$Res> {
  __$$NetworkErrorImplCopyWithImpl(
      _$NetworkErrorImpl _value, $Res Function(_$NetworkErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$NetworkErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NetworkErrorImpl implements NetworkError {
  const _$NetworkErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.network(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkErrorImplCopyWith<_$NetworkErrorImpl> get copyWith =>
      __$$NetworkErrorImplCopyWithImpl<_$NetworkErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return network(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return network?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return network(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return network?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(this);
    }
    return orElse();
  }
}

abstract class NetworkError implements AppError {
  const factory NetworkError(final String message) = _$NetworkErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkErrorImplCopyWith<_$NetworkErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DatabaseErrorImplCopyWith<$Res> {
  factory _$$DatabaseErrorImplCopyWith(
          _$DatabaseErrorImpl value, $Res Function(_$DatabaseErrorImpl) then) =
      __$$DatabaseErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$DatabaseErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$DatabaseErrorImpl>
    implements _$$DatabaseErrorImplCopyWith<$Res> {
  __$$DatabaseErrorImplCopyWithImpl(
      _$DatabaseErrorImpl _value, $Res Function(_$DatabaseErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$DatabaseErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DatabaseErrorImpl implements DatabaseError {
  const _$DatabaseErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.database(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DatabaseErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DatabaseErrorImplCopyWith<_$DatabaseErrorImpl> get copyWith =>
      __$$DatabaseErrorImplCopyWithImpl<_$DatabaseErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return database(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return database?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (database != null) {
      return database(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return database(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return database?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (database != null) {
      return database(this);
    }
    return orElse();
  }
}

abstract class DatabaseError implements AppError {
  const factory DatabaseError(final String message) = _$DatabaseErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DatabaseErrorImplCopyWith<_$DatabaseErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AuthenticationErrorImplCopyWith<$Res> {
  factory _$$AuthenticationErrorImplCopyWith(_$AuthenticationErrorImpl value,
          $Res Function(_$AuthenticationErrorImpl) then) =
      __$$AuthenticationErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$AuthenticationErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$AuthenticationErrorImpl>
    implements _$$AuthenticationErrorImplCopyWith<$Res> {
  __$$AuthenticationErrorImplCopyWithImpl(_$AuthenticationErrorImpl _value,
      $Res Function(_$AuthenticationErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$AuthenticationErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AuthenticationErrorImpl implements AuthenticationError {
  const _$AuthenticationErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.authentication(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthenticationErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthenticationErrorImplCopyWith<_$AuthenticationErrorImpl> get copyWith =>
      __$$AuthenticationErrorImplCopyWithImpl<_$AuthenticationErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return authentication(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return authentication?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return authentication(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return authentication?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(this);
    }
    return orElse();
  }
}

abstract class AuthenticationError implements AppError {
  const factory AuthenticationError(final String message) =
      _$AuthenticationErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthenticationErrorImplCopyWith<_$AuthenticationErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ValidationErrorImplCopyWith<$Res> {
  factory _$$ValidationErrorImplCopyWith(_$ValidationErrorImpl value,
          $Res Function(_$ValidationErrorImpl) then) =
      __$$ValidationErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String field, String message});
}

/// @nodoc
class __$$ValidationErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$ValidationErrorImpl>
    implements _$$ValidationErrorImplCopyWith<$Res> {
  __$$ValidationErrorImplCopyWithImpl(
      _$ValidationErrorImpl _value, $Res Function(_$ValidationErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? field = null,
    Object? message = null,
  }) {
    return _then(_$ValidationErrorImpl(
      null == field
          ? _value.field
          : field // ignore: cast_nullable_to_non_nullable
              as String,
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ValidationErrorImpl implements ValidationError {
  const _$ValidationErrorImpl(this.field, this.message);

  @override
  final String field;
  @override
  final String message;

  @override
  String toString() {
    return 'AppError.validation(field: $field, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidationErrorImpl &&
            (identical(other.field, field) || other.field == field) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, field, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValidationErrorImplCopyWith<_$ValidationErrorImpl> get copyWith =>
      __$$ValidationErrorImplCopyWithImpl<_$ValidationErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return validation(field, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return validation?.call(field, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(field, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return validation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return validation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(this);
    }
    return orElse();
  }
}

abstract class ValidationError implements AppError {
  const factory ValidationError(final String field, final String message) =
      _$ValidationErrorImpl;

  String get field;
  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValidationErrorImplCopyWith<_$ValidationErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PermissionErrorImplCopyWith<$Res> {
  factory _$$PermissionErrorImplCopyWith(_$PermissionErrorImpl value,
          $Res Function(_$PermissionErrorImpl) then) =
      __$$PermissionErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$PermissionErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$PermissionErrorImpl>
    implements _$$PermissionErrorImplCopyWith<$Res> {
  __$$PermissionErrorImplCopyWithImpl(
      _$PermissionErrorImpl _value, $Res Function(_$PermissionErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$PermissionErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PermissionErrorImpl implements PermissionError {
  const _$PermissionErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.permission(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PermissionErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PermissionErrorImplCopyWith<_$PermissionErrorImpl> get copyWith =>
      __$$PermissionErrorImplCopyWithImpl<_$PermissionErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return permission(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return permission?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return permission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return permission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(this);
    }
    return orElse();
  }
}

abstract class PermissionError implements AppError {
  const factory PermissionError(final String message) = _$PermissionErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PermissionErrorImplCopyWith<_$PermissionErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NotFoundErrorImplCopyWith<$Res> {
  factory _$$NotFoundErrorImplCopyWith(
          _$NotFoundErrorImpl value, $Res Function(_$NotFoundErrorImpl) then) =
      __$$NotFoundErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String resource});
}

/// @nodoc
class __$$NotFoundErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$NotFoundErrorImpl>
    implements _$$NotFoundErrorImplCopyWith<$Res> {
  __$$NotFoundErrorImplCopyWithImpl(
      _$NotFoundErrorImpl _value, $Res Function(_$NotFoundErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? resource = null,
  }) {
    return _then(_$NotFoundErrorImpl(
      null == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NotFoundErrorImpl implements NotFoundError {
  const _$NotFoundErrorImpl(this.resource);

  @override
  final String resource;

  @override
  String toString() {
    return 'AppError.notFound(resource: $resource)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotFoundErrorImpl &&
            (identical(other.resource, resource) ||
                other.resource == resource));
  }

  @override
  int get hashCode => Object.hash(runtimeType, resource);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotFoundErrorImplCopyWith<_$NotFoundErrorImpl> get copyWith =>
      __$$NotFoundErrorImplCopyWithImpl<_$NotFoundErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return notFound(resource);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return notFound?.call(resource);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(resource);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return notFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return notFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(this);
    }
    return orElse();
  }
}

abstract class NotFoundError implements AppError {
  const factory NotFoundError(final String resource) = _$NotFoundErrorImpl;

  String get resource;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotFoundErrorImplCopyWith<_$NotFoundErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConflictErrorImplCopyWith<$Res> {
  factory _$$ConflictErrorImplCopyWith(
          _$ConflictErrorImpl value, $Res Function(_$ConflictErrorImpl) then) =
      __$$ConflictErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ConflictErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$ConflictErrorImpl>
    implements _$$ConflictErrorImplCopyWith<$Res> {
  __$$ConflictErrorImplCopyWithImpl(
      _$ConflictErrorImpl _value, $Res Function(_$ConflictErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ConflictErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ConflictErrorImpl implements ConflictError {
  const _$ConflictErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.conflict(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConflictErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConflictErrorImplCopyWith<_$ConflictErrorImpl> get copyWith =>
      __$$ConflictErrorImplCopyWithImpl<_$ConflictErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return conflict(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return conflict?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (conflict != null) {
      return conflict(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return conflict(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return conflict?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (conflict != null) {
      return conflict(this);
    }
    return orElse();
  }
}

abstract class ConflictError implements AppError {
  const factory ConflictError(final String message) = _$ConflictErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConflictErrorImplCopyWith<_$ConflictErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RateLimitErrorImplCopyWith<$Res> {
  factory _$$RateLimitErrorImplCopyWith(_$RateLimitErrorImpl value,
          $Res Function(_$RateLimitErrorImpl) then) =
      __$$RateLimitErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$RateLimitErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$RateLimitErrorImpl>
    implements _$$RateLimitErrorImplCopyWith<$Res> {
  __$$RateLimitErrorImplCopyWithImpl(
      _$RateLimitErrorImpl _value, $Res Function(_$RateLimitErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$RateLimitErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RateLimitErrorImpl implements RateLimitError {
  const _$RateLimitErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.rateLimit(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RateLimitErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RateLimitErrorImplCopyWith<_$RateLimitErrorImpl> get copyWith =>
      __$$RateLimitErrorImplCopyWithImpl<_$RateLimitErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return rateLimit(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return rateLimit?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (rateLimit != null) {
      return rateLimit(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return rateLimit(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return rateLimit?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (rateLimit != null) {
      return rateLimit(this);
    }
    return orElse();
  }
}

abstract class RateLimitError implements AppError {
  const factory RateLimitError(final String message) = _$RateLimitErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RateLimitErrorImplCopyWith<_$RateLimitErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ServerErrorImplCopyWith<$Res> {
  factory _$$ServerErrorImplCopyWith(
          _$ServerErrorImpl value, $Res Function(_$ServerErrorImpl) then) =
      __$$ServerErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ServerErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$ServerErrorImpl>
    implements _$$ServerErrorImplCopyWith<$Res> {
  __$$ServerErrorImplCopyWithImpl(
      _$ServerErrorImpl _value, $Res Function(_$ServerErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ServerErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ServerErrorImpl implements ServerError {
  const _$ServerErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.server(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServerErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ServerErrorImplCopyWith<_$ServerErrorImpl> get copyWith =>
      __$$ServerErrorImplCopyWithImpl<_$ServerErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return server(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return server?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (server != null) {
      return server(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return server(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return server?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (server != null) {
      return server(this);
    }
    return orElse();
  }
}

abstract class ServerError implements AppError {
  const factory ServerError(final String message) = _$ServerErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ServerErrorImplCopyWith<_$ServerErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ClientErrorImplCopyWith<$Res> {
  factory _$$ClientErrorImplCopyWith(
          _$ClientErrorImpl value, $Res Function(_$ClientErrorImpl) then) =
      __$$ClientErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ClientErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$ClientErrorImpl>
    implements _$$ClientErrorImplCopyWith<$Res> {
  __$$ClientErrorImplCopyWithImpl(
      _$ClientErrorImpl _value, $Res Function(_$ClientErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ClientErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ClientErrorImpl implements ClientError {
  const _$ClientErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.client(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ClientErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ClientErrorImplCopyWith<_$ClientErrorImpl> get copyWith =>
      __$$ClientErrorImplCopyWithImpl<_$ClientErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return client(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return client?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (client != null) {
      return client(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return client(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return client?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (client != null) {
      return client(this);
    }
    return orElse();
  }
}

abstract class ClientError implements AppError {
  const factory ClientError(final String message) = _$ClientErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ClientErrorImplCopyWith<_$ClientErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SyncErrorImplCopyWith<$Res> {
  factory _$$SyncErrorImplCopyWith(
          _$SyncErrorImpl value, $Res Function(_$SyncErrorImpl) then) =
      __$$SyncErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$SyncErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$SyncErrorImpl>
    implements _$$SyncErrorImplCopyWith<$Res> {
  __$$SyncErrorImplCopyWithImpl(
      _$SyncErrorImpl _value, $Res Function(_$SyncErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$SyncErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SyncErrorImpl implements SyncError {
  const _$SyncErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.sync(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SyncErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SyncErrorImplCopyWith<_$SyncErrorImpl> get copyWith =>
      __$$SyncErrorImplCopyWithImpl<_$SyncErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return sync(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return sync?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (sync != null) {
      return sync(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return sync(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return sync?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (sync != null) {
      return sync(this);
    }
    return orElse();
  }
}

abstract class SyncError implements AppError {
  const factory SyncError(final String message) = _$SyncErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SyncErrorImplCopyWith<_$SyncErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CacheErrorImplCopyWith<$Res> {
  factory _$$CacheErrorImplCopyWith(
          _$CacheErrorImpl value, $Res Function(_$CacheErrorImpl) then) =
      __$$CacheErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$CacheErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$CacheErrorImpl>
    implements _$$CacheErrorImplCopyWith<$Res> {
  __$$CacheErrorImplCopyWithImpl(
      _$CacheErrorImpl _value, $Res Function(_$CacheErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$CacheErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CacheErrorImpl implements CacheError {
  const _$CacheErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.cache(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CacheErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CacheErrorImplCopyWith<_$CacheErrorImpl> get copyWith =>
      __$$CacheErrorImplCopyWithImpl<_$CacheErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return cache(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return cache?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (cache != null) {
      return cache(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return cache(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return cache?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (cache != null) {
      return cache(this);
    }
    return orElse();
  }
}

abstract class CacheError implements AppError {
  const factory CacheError(final String message) = _$CacheErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CacheErrorImplCopyWith<_$CacheErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnknownErrorImplCopyWith<$Res> {
  factory _$$UnknownErrorImplCopyWith(
          _$UnknownErrorImpl value, $Res Function(_$UnknownErrorImpl) then) =
      __$$UnknownErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$UnknownErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$UnknownErrorImpl>
    implements _$$UnknownErrorImplCopyWith<$Res> {
  __$$UnknownErrorImplCopyWithImpl(
      _$UnknownErrorImpl _value, $Res Function(_$UnknownErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$UnknownErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UnknownErrorImpl implements UnknownError {
  const _$UnknownErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AppError.unknown(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnknownErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnknownErrorImplCopyWith<_$UnknownErrorImpl> get copyWith =>
      __$$UnknownErrorImplCopyWithImpl<_$UnknownErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message) network,
    required TResult Function(String message) database,
    required TResult Function(String message) authentication,
    required TResult Function(String field, String message) validation,
    required TResult Function(String message) permission,
    required TResult Function(String resource) notFound,
    required TResult Function(String message) conflict,
    required TResult Function(String message) rateLimit,
    required TResult Function(String message) server,
    required TResult Function(String message) client,
    required TResult Function(String message) sync,
    required TResult Function(String message) cache,
    required TResult Function(String message) unknown,
  }) {
    return unknown(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message)? network,
    TResult? Function(String message)? database,
    TResult? Function(String message)? authentication,
    TResult? Function(String field, String message)? validation,
    TResult? Function(String message)? permission,
    TResult? Function(String resource)? notFound,
    TResult? Function(String message)? conflict,
    TResult? Function(String message)? rateLimit,
    TResult? Function(String message)? server,
    TResult? Function(String message)? client,
    TResult? Function(String message)? sync,
    TResult? Function(String message)? cache,
    TResult? Function(String message)? unknown,
  }) {
    return unknown?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message)? network,
    TResult Function(String message)? database,
    TResult Function(String message)? authentication,
    TResult Function(String field, String message)? validation,
    TResult Function(String message)? permission,
    TResult Function(String resource)? notFound,
    TResult Function(String message)? conflict,
    TResult Function(String message)? rateLimit,
    TResult Function(String message)? server,
    TResult Function(String message)? client,
    TResult Function(String message)? sync,
    TResult Function(String message)? cache,
    TResult Function(String message)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkError value) network,
    required TResult Function(DatabaseError value) database,
    required TResult Function(AuthenticationError value) authentication,
    required TResult Function(ValidationError value) validation,
    required TResult Function(PermissionError value) permission,
    required TResult Function(NotFoundError value) notFound,
    required TResult Function(ConflictError value) conflict,
    required TResult Function(RateLimitError value) rateLimit,
    required TResult Function(ServerError value) server,
    required TResult Function(ClientError value) client,
    required TResult Function(SyncError value) sync,
    required TResult Function(CacheError value) cache,
    required TResult Function(UnknownError value) unknown,
  }) {
    return unknown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkError value)? network,
    TResult? Function(DatabaseError value)? database,
    TResult? Function(AuthenticationError value)? authentication,
    TResult? Function(ValidationError value)? validation,
    TResult? Function(PermissionError value)? permission,
    TResult? Function(NotFoundError value)? notFound,
    TResult? Function(ConflictError value)? conflict,
    TResult? Function(RateLimitError value)? rateLimit,
    TResult? Function(ServerError value)? server,
    TResult? Function(ClientError value)? client,
    TResult? Function(SyncError value)? sync,
    TResult? Function(CacheError value)? cache,
    TResult? Function(UnknownError value)? unknown,
  }) {
    return unknown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkError value)? network,
    TResult Function(DatabaseError value)? database,
    TResult Function(AuthenticationError value)? authentication,
    TResult Function(ValidationError value)? validation,
    TResult Function(PermissionError value)? permission,
    TResult Function(NotFoundError value)? notFound,
    TResult Function(ConflictError value)? conflict,
    TResult Function(RateLimitError value)? rateLimit,
    TResult Function(ServerError value)? server,
    TResult Function(ClientError value)? client,
    TResult Function(SyncError value)? sync,
    TResult Function(CacheError value)? cache,
    TResult Function(UnknownError value)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(this);
    }
    return orElse();
  }
}

abstract class UnknownError implements AppError {
  const factory UnknownError(final String message) = _$UnknownErrorImpl;

  String get message;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnknownErrorImplCopyWith<_$UnknownErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
