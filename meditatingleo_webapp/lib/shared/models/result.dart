import 'package:freezed_annotation/freezed_annotation.dart';

part 'result.freezed.dart';

/// A generic result type that represents either success or failure.
///
/// This is used throughout the application to handle operations that can fail
/// in a type-safe manner, avoiding exceptions for expected error cases.
///
/// Example usage:
/// ```dart
/// Result<User, AppError> result = await userRepository.getUser(id);
/// result.when(
///   success: (user) => print('Got user: ${user.name}'),
///   failure: (error) => print('Error: ${error.message}'),
/// );
/// ```
@freezed
class Result<T, E> with _$Result<T, E> {
  /// Creates a successful result containing data of type [T].
  const factory Result.success(T data) = Success<T, E>;

  /// Creates a failed result containing an error of type [E].
  const factory Result.failure(E error) = Failure<T, E>;
}

/// Extension methods for [Result] to provide convenient access patterns.
extension ResultExtensions<T, E> on Result<T, E> {
  /// Returns true if this result represents a success.
  bool get isSuccess => when(
        success: (_) => true,
        failure: (_) => false,
      );

  /// Returns true if this result represents a failure.
  bool get isFailure => !isSuccess;

  /// Returns the data if this is a success, null otherwise.
  T? get data => when(
        success: (data) => data,
        failure: (_) => null,
      );

  /// Returns the error if this is a failure, null otherwise.
  E? get error => when(
        success: (_) => null,
        failure: (error) => error,
      );

  /// Maps the success data to a new type while preserving failure.
  Result<U, E> mapData<U>(U Function(T) mapper) {
    return when(
      success: (data) => Result.success(mapper(data)),
      failure: (error) => Result.failure(error),
    );
  }

  /// Maps the error to a new type while preserving success.
  Result<T, F> mapError<F>(F Function(E) mapper) {
    return when(
      success: (data) => Result.success(data),
      failure: (error) => Result.failure(mapper(error)),
    );
  }

  /// Chains another operation that returns a Result.
  Result<U, E> flatMap<U>(Result<U, E> Function(T) mapper) {
    return when(
      success: (data) => mapper(data),
      failure: (error) => Result.failure(error),
    );
  }

  /// Chains another async operation that returns a Future<Result>.
  Future<Result<U, E>> flatMapAsync<U>(
      Future<Result<U, E>> Function(T) mapper) async {
    return when(
      success: (data) async => await mapper(data),
      failure: (error) => Future.value(Result.failure(error)),
    );
  }

  /// Returns the data if success, or throws the error if failure.
  T unwrap() {
    return when(
      success: (data) => data,
      failure: (error) => throw error as Object,
    );
  }

  /// Returns the data if success, or the provided default value if failure.
  T unwrapOr(T defaultValue) {
    return when(
      success: (data) => data,
      failure: (_) => defaultValue,
    );
  }

  /// Returns the data if success, or the result of calling the provided function if failure.
  T unwrapOrElse(T Function(E) defaultFunction) {
    return when(
      success: (data) => data,
      failure: (error) => defaultFunction(error),
    );
  }
}

/// Utility functions for working with [Result] types.
class ResultUtils {
  /// Combines multiple results into a single result containing a list.
  /// If any result is a failure, returns the first failure.
  static Result<List<T>, E> combine<T, E>(List<Result<T, E>> results) {
    final List<T> successData = [];

    for (final result in results) {
      final data = result.when(
        success: (data) => data,
        failure: (error) => null,
      );

      if (data != null) {
        successData.add(data);
      } else {
        // Return the first failure encountered
        return result.when(
          success: (_) => throw StateError('Unexpected success'),
          failure: (error) => Result.failure(error),
        );
      }
    }

    return Result.success(successData);
  }

  /// Executes an async operation and wraps the result in a Result type.
  /// Catches exceptions and converts them to failures.
  static Future<Result<T, E>> tryAsync<T, E>(
    Future<T> Function() operation,
    E Function(Object error, StackTrace stackTrace) errorMapper,
  ) async {
    try {
      final data = await operation();
      return Result.success(data);
    } catch (error, stackTrace) {
      return Result.failure(errorMapper(error, stackTrace));
    }
  }

  /// Executes a synchronous operation and wraps the result in a Result type.
  /// Catches exceptions and converts them to failures.
  static Result<T, E> trySync<T, E>(
    T Function() operation,
    E Function(Object error, StackTrace stackTrace) errorMapper,
  ) {
    try {
      final data = operation();
      return Result.success(data);
    } catch (error, stackTrace) {
      return Result.failure(errorMapper(error, stackTrace));
    }
  }
}
