import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_strategy/url_strategy.dart';

import '../core/constants/app_constants.dart';
import '../shared/providers/app_providers.dart';

/// Main application widget for the MeditatingLeo Web Application.
///
/// This widget serves as the root of the application, configuring
/// the MaterialApp with routing, theming, and global providers.
/// It follows Material Design 3 guidelines and modern Flutter patterns.
class WebApp extends ConsumerWidget {
  /// Creates the main web application widget.
  const WebApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Configure URL strategy for clean web URLs
    setPathUrlStrategy();

    final router = ref.watch(routerProvider);
    final theme = ref.watch(themeProvider);

    return MaterialApp.router(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      
      // Theme configuration
      theme: theme,
      
      // Router configuration
      routerConfig: router,
      
      // Localization configuration (placeholder for future implementation)
      supportedLocales: const [
        Locale('en', 'US'),
      ],
      
      // Builder for additional app-level configuration
      builder: (context, child) {
        return _AppWrapper(child: child);
      },
    );
  }
}

/// Wrapper widget for app-level configuration and error handling.
///
/// This widget provides a consistent error boundary and global
/// configuration that applies to all screens in the application.
class _AppWrapper extends StatelessWidget {
  /// Child widget to wrap
  final Widget? child;

  /// Creates an app wrapper widget.
  const _AppWrapper({this.child});

  @override
  Widget build(BuildContext context) {
    return MediaQuery(
      // Ensure text scaling doesn't break layouts
      data: MediaQuery.of(context).copyWith(
        textScaler: MediaQuery.of(context).textScaler.clamp(
          minScaleFactor: 0.8,
          maxScaleFactor: 1.2,
        ),
      ),
      child: child ?? const SizedBox.shrink(),
    );
  }
}

/// Error widget for handling app-level errors.
///
/// This widget displays when there are critical errors that
/// prevent the normal app flow from functioning.
class AppErrorWidget extends StatelessWidget {
  /// Error details to display
  final FlutterErrorDetails errorDetails;

  /// Creates an app error widget.
  const AppErrorWidget({
    super.key,
    required this.errorDetails,
  });

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      home: Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                Text(
                  'Something went wrong',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Please refresh the page to try again.',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                if (AppConstants.isDebugMode) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${errorDetails.exception}',
                    style: Theme.of(context).textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                ],
                const SizedBox(height: 24),
                FilledButton(
                  onPressed: () {
                    // In a web app, we can reload the page
                    // ignore: avoid_web_libraries_in_flutter
                    // html.window.location.reload();
                  },
                  child: const Text('Refresh Page'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
