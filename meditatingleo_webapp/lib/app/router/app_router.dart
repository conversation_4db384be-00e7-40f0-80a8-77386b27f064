import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/home/<USER>/pages/home_page.dart';
import 'routes.dart';

/// Router configuration for the MeditatingLeo Web Application.
///
/// This class configures the GoRouter with all application routes,
/// navigation guards, and error handling for the web application.
class AppRouter {
  // Private constructor to prevent instantiation
  AppRouter._();

  /// Creates and configures the GoRouter instance
  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: AppRoutes.home,
      debugLogDiagnostics: true,
      
      // Error handling
      errorBuilder: (context, state) => _ErrorPage(
        error: state.error?.toString() ?? 'Unknown error',
        path: state.uri.toString(),
      ),
      
      // Route definitions
      routes: [
        // Home route
        GoRoute(
          path: AppRoutes.home,
          name: AppRoutes.homeName,
          builder: (context, state) => const HomePage(),
        ),

        // Authentication routes (placeholder for future implementation)
        GoRoute(
          path: AppRoutes.login,
          name: AppRoutes.loginName,
          builder: (context, state) => const _PlaceholderPage(
            title: 'Login',
            message: 'Login page will be implemented in future tasks',
          ),
        ),

        GoRoute(
          path: AppRoutes.register,
          name: AppRoutes.registerName,
          builder: (context, state) => const _PlaceholderPage(
            title: 'Register',
            message: 'Registration page will be implemented in future tasks',
          ),
        ),

        // Journal routes (placeholder for future implementation)
        GoRoute(
          path: AppRoutes.journal,
          name: AppRoutes.journalName,
          builder: (context, state) => const _PlaceholderPage(
            title: 'Journal',
            message: 'Journal page will be implemented in future tasks',
          ),
        ),

        // Settings routes (placeholder for future implementation)
        GoRoute(
          path: AppRoutes.settings,
          name: AppRoutes.settingsName,
          builder: (context, state) => const _PlaceholderPage(
            title: 'Settings',
            message: 'Settings page will be implemented in future tasks',
          ),
        ),

        // About page
        GoRoute(
          path: AppRoutes.about,
          name: AppRoutes.aboutName,
          builder: (context, state) => const _AboutPage(),
        ),
      ],
    );
  }
}

/// Error page widget for handling navigation errors
class _ErrorPage extends StatelessWidget {
  final String error;
  final String path;

  const _ErrorPage({
    required this.error,
    required this.path,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 24),
              Text(
                'Page Not Found',
                style: theme.textTheme.headlineMedium,
              ),
              const SizedBox(height: 16),
              Text(
                'The page "$path" could not be found.',
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Error: $error',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              FilledButton.icon(
                onPressed: () => context.go(AppRoutes.home),
                icon: const Icon(Icons.home),
                label: const Text('Go Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Placeholder page for routes not yet implemented
class _PlaceholderPage extends StatelessWidget {
  final String title;
  final String message;

  const _PlaceholderPage({
    required this.title,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.construction,
                size: 64,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: 24),
              Text(
                'Coming Soon',
                style: theme.textTheme.headlineMedium,
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              FilledButton.icon(
                onPressed: () => context.go(AppRoutes.home),
                icon: const Icon(Icons.home),
                label: const Text('Go Home'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// About page with application information
class _AboutPage extends StatelessWidget {
  const _AboutPage();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('About'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(32.0),
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 600),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ClarityByMeditatingLeo',
                  style: theme.textTheme.headlineLarge,
                ),
                const SizedBox(height: 16),
                Text(
                  'A desktop-optimized journaling experience for clarity and mindfulness.',
                  style: theme.textTheme.bodyLarge,
                ),
                const SizedBox(height: 32),
                Text(
                  'Features',
                  style: theme.textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),
                _buildFeatureList(theme),
                const SizedBox(height: 32),
                Text(
                  'Technology',
                  style: theme.textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),
                _buildTechnologyList(theme),
                const SizedBox(height: 32),
                FilledButton.icon(
                  onPressed: () => context.go(AppRoutes.home),
                  icon: const Icon(Icons.home),
                  label: const Text('Go Home'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureList(ThemeData theme) {
    final features = [
      'Responsive design for all screen sizes',
      'Progressive Web App (PWA) capabilities',
      'Modern Material Design 3 interface',
      'Offline functionality with service worker',
      'Real-time data synchronization',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) => Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.check_circle,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                feature,
                style: theme.textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildTechnologyList(ThemeData theme) {
    final technologies = [
      'Flutter 3.24.0+ for cross-platform development',
      'Riverpod for modern state management',
      'Supabase for backend services',
      'Material Design 3 for modern UI',
      'GoRouter for declarative navigation',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: technologies.map((tech) => Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              Icons.code,
              size: 20,
              color: theme.colorScheme.secondary,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                tech,
                style: theme.textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }
}
