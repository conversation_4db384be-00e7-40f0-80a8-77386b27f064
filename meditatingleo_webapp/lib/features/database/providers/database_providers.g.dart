// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$webSupabaseHash() => r'475b84b9c6502c20b112fd46ae4ebc72d2d15ff8';

/// Provides the Supabase client instance for web database operations.
///
/// This provider gives access to the configured Supabase client
/// for the web application with web-specific optimizations.
///
/// Copied from [webSupabase].
@ProviderFor(webSupabase)
final webSupabaseProvider = AutoDisposeProvider<SupabaseClient>.internal(
  webSupabase,
  name: r'webSupabaseProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$webSupabaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebSupabaseRef = AutoDisposeProviderRef<SupabaseClient>;
String _$webDatabaseServiceHash() =>
    r'8ec3c98ab7ae98907dc77a96dc3ddc502bf39eb0';

/// Provides the web database service instance.
///
/// This service handles low-level database operations with
/// web-specific optimizations and error handling.
///
/// Copied from [webDatabaseService].
@ProviderFor(webDatabaseService)
final webDatabaseServiceProvider =
    AutoDisposeProvider<WebDatabaseService>.internal(
  webDatabaseService,
  name: r'webDatabaseServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webDatabaseServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebDatabaseServiceRef = AutoDisposeProviderRef<WebDatabaseService>;
String _$realTimeServiceHash() => r'd84d57d73078b6675b67d7305abb8e164d8fa8a5';

/// Provides the real-time service instance for live updates.
///
/// This service manages real-time subscriptions to database changes
/// with automatic reconnection and subscription management.
///
/// Copied from [realTimeService].
@ProviderFor(realTimeService)
final realTimeServiceProvider = AutoDisposeProvider<RealTimeService>.internal(
  realTimeService,
  name: r'realTimeServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realTimeServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RealTimeServiceRef = AutoDisposeProviderRef<RealTimeService>;
String _$cacheServiceHash() => r'cb42aa8fc3d415df1a8d5e8a3f36d45b25a0f16b';

/// Provides the cache service instance for web-specific caching.
///
/// This service handles browser storage with intelligent caching,
/// automatic expiration, and size management.
///
/// Copied from [cacheService].
@ProviderFor(cacheService)
final cacheServiceProvider = AutoDisposeProvider<CacheService>.internal(
  cacheService,
  name: r'cacheServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$cacheServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CacheServiceRef = AutoDisposeProviderRef<CacheService>;
String _$webDatabaseRepositoryHash() =>
    r'98e73cff377793d95498e5fcf4f9f90879bd0788';

/// Provides the web database repository instance.
///
/// This repository provides a clean interface for database operations
/// following the repository pattern with consistent error handling.
///
/// Copied from [webDatabaseRepository].
@ProviderFor(webDatabaseRepository)
final webDatabaseRepositoryProvider =
    AutoDisposeProvider<WebDatabaseRepository>.internal(
  webDatabaseRepository,
  name: r'webDatabaseRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$webDatabaseRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef WebDatabaseRepositoryRef
    = AutoDisposeProviderRef<WebDatabaseRepository>;
String _$databaseInitializationHash() =>
    r'970453b3ae5010e84158259614528aba5007fb1a';

/// Provides database initialization status.
///
/// This provider initializes the database and returns the initialization status.
/// It should be watched early in the app lifecycle to ensure database readiness.
///
/// Copied from [databaseInitialization].
@ProviderFor(databaseInitialization)
final databaseInitializationProvider = AutoDisposeFutureProvider<bool>.internal(
  databaseInitialization,
  name: r'databaseInitializationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseInitializationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseInitializationRef = AutoDisposeFutureProviderRef<bool>;
String _$connectionStatusHash() => r'24d4477e0db55a675c7d2af235e7c8ada7e97ff9';

/// Provides the current real-time connection status.
///
/// This provider returns true if the real-time connection is active,
/// false otherwise. Useful for showing connection status in the UI.
///
/// Copied from [connectionStatus].
@ProviderFor(connectionStatus)
final connectionStatusProvider = AutoDisposeProvider<bool>.internal(
  connectionStatus,
  name: r'connectionStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$connectionStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ConnectionStatusRef = AutoDisposeProviderRef<bool>;
String _$cacheStatusHash() => r'56444626b943441fa1f75baaab494a3de33a4e2c';

/// Provides cache status and statistics.
///
/// This provider returns cache information including size, entries,
/// hit rate, and usage statistics for monitoring and debugging.
///
/// Copied from [cacheStatus].
@ProviderFor(cacheStatus)
final cacheStatusProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
  cacheStatus,
  name: r'cacheStatusProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$cacheStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CacheStatusRef = AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$connectionStatusStreamHash() =>
    r'199200c0dcfc27cd963e42b1db012c82f2fcedce';

/// Provides a stream of real-time connection status changes.
///
/// This provider emits connection status updates for reactive UI updates
/// when the real-time connection state changes.
///
/// Copied from [connectionStatusStream].
@ProviderFor(connectionStatusStream)
final connectionStatusStreamProvider = AutoDisposeStreamProvider<bool>.internal(
  connectionStatusStream,
  name: r'connectionStatusStreamProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$connectionStatusStreamHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ConnectionStatusStreamRef = AutoDisposeStreamProviderRef<bool>;
String _$databaseHealthHash() => r'cd622c6a16492b7122ba1a5623b12aeb62f846ea';

/// Provides database health status.
///
/// This provider performs basic health checks on the database
/// and returns overall health status information.
///
/// Copied from [databaseHealth].
@ProviderFor(databaseHealth)
final databaseHealthProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
  databaseHealth,
  name: r'databaseHealthProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseHealthHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DatabaseHealthRef = AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$activeSubscriptionsCountHash() =>
    r'4a3d42b2fd1070b380d45a124a1bf83b2858bf46';

/// Provides active real-time subscriptions count.
///
/// This provider returns the number of active real-time subscriptions
/// for monitoring and debugging purposes.
///
/// Copied from [activeSubscriptionsCount].
@ProviderFor(activeSubscriptionsCount)
final activeSubscriptionsCountProvider = AutoDisposeProvider<int>.internal(
  activeSubscriptionsCount,
  name: r'activeSubscriptionsCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activeSubscriptionsCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActiveSubscriptionsCountRef = AutoDisposeProviderRef<int>;
String _$activeSubscriptionChannelsHash() =>
    r'59a0b824b92fd70135d3f006e018170b8d4c4514';

/// Provides a list of active subscription channel names.
///
/// This provider returns the names of all active real-time channels
/// for debugging and monitoring subscription management.
///
/// Copied from [activeSubscriptionChannels].
@ProviderFor(activeSubscriptionChannels)
final activeSubscriptionChannelsProvider =
    AutoDisposeProvider<List<String>>.internal(
  activeSubscriptionChannels,
  name: r'activeSubscriptionChannelsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activeSubscriptionChannelsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ActiveSubscriptionChannelsRef = AutoDisposeProviderRef<List<String>>;
String _$databaseSubscriptionManagerHash() =>
    r'eeabf1f86bd88312b043e00cd07750f3c54e65e5';

/// Provider for managing database table subscriptions.
///
/// This provider helps manage real-time subscriptions to specific tables
/// with automatic cleanup and error handling.
///
/// Copied from [DatabaseSubscriptionManager].
@ProviderFor(DatabaseSubscriptionManager)
final databaseSubscriptionManagerProvider = AutoDisposeNotifierProvider<
    DatabaseSubscriptionManager, Map<String, bool>>.internal(
  DatabaseSubscriptionManager.new,
  name: r'databaseSubscriptionManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$databaseSubscriptionManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DatabaseSubscriptionManager = AutoDisposeNotifier<Map<String, bool>>;
String _$cacheManagerHash() => r'07f14f303b1984fc2ddd5268d0353a8c539b59c2';

/// Provider for cache management operations.
///
/// This provider provides methods for cache operations like clearing,
/// evicting expired entries, and getting cache statistics.
///
/// Copied from [CacheManager].
@ProviderFor(CacheManager)
final cacheManagerProvider =
    AutoDisposeNotifierProvider<CacheManager, Map<String, dynamic>>.internal(
  CacheManager.new,
  name: r'cacheManagerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$cacheManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CacheManager = AutoDisposeNotifier<Map<String, dynamic>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
