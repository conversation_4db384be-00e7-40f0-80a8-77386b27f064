import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_webapp/features/database/data/services/web_database_service.dart';
import 'package:meditatingleo_webapp/features/database/data/services/real_time_service.dart';
import 'package:meditatingleo_webapp/features/database/data/services/cache_service.dart';
import 'package:meditatingleo_webapp/features/database/data/repositories/web_database_repository.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

part 'database_providers.g.dart';

/// Provides the Supabase client instance for web database operations.
///
/// This provider gives access to the configured Supabase client
/// for the web application with web-specific optimizations.
@riverpod
SupabaseClient webSupabase(WebSupabaseRef ref) {
  return Supabase.instance.client;
}

/// Provides the web database service instance.
///
/// This service handles low-level database operations with
/// web-specific optimizations and error handling.
@riverpod
WebDatabaseService webDatabaseService(WebDatabaseServiceRef ref) {
  final supabaseClient = ref.watch(webSupabaseProvider);
  return WebDatabaseService(supabaseClient);
}

/// Provides the real-time service instance for live updates.
///
/// This service manages real-time subscriptions to database changes
/// with automatic reconnection and subscription management.
@riverpod
RealTimeService realTimeService(RealTimeServiceRef ref) {
  final supabaseClient = ref.watch(webSupabaseProvider);
  return RealTimeService(supabaseClient);
}

/// Provides the cache service instance for web-specific caching.
///
/// This service handles browser storage with intelligent caching,
/// automatic expiration, and size management.
@riverpod
CacheService cacheService(CacheServiceRef ref) {
  return CacheService();
}

/// Provides the web database repository instance.
///
/// This repository provides a clean interface for database operations
/// following the repository pattern with consistent error handling.
@riverpod
WebDatabaseRepository webDatabaseRepository(WebDatabaseRepositoryRef ref) {
  final databaseService = ref.watch(webDatabaseServiceProvider);
  return WebDatabaseRepository(databaseService);
}

/// Provides database initialization status.
///
/// This provider initializes the database and returns the initialization status.
/// It should be watched early in the app lifecycle to ensure database readiness.
@riverpod
Future<bool> databaseInitialization(DatabaseInitializationRef ref) async {
  final repository = ref.watch(webDatabaseRepositoryProvider);
  final result = await repository.initialize();
  
  return result.when(
    success: (initialized) => initialized,
    failure: (error) => throw error,
  );
}

/// Provides the current real-time connection status.
///
/// This provider returns true if the real-time connection is active,
/// false otherwise. Useful for showing connection status in the UI.
@riverpod
bool connectionStatus(ConnectionStatusRef ref) {
  final realTimeService = ref.watch(realTimeServiceProvider);
  return realTimeService.isConnected;
}

/// Provides cache status and statistics.
///
/// This provider returns cache information including size, entries,
/// hit rate, and usage statistics for monitoring and debugging.
@riverpod
Future<Map<String, dynamic>> cacheStatus(CacheStatusRef ref) async {
  final cacheService = ref.watch(cacheServiceProvider);
  final result = await cacheService.getStatus();
  
  return result.when(
    success: (status) => status,
    failure: (error) => throw error,
  );
}

/// Provides a stream of real-time connection status changes.
///
/// This provider emits connection status updates for reactive UI updates
/// when the real-time connection state changes.
@riverpod
Stream<bool> connectionStatusStream(ConnectionStatusStreamRef ref) {
  final realTimeService = ref.watch(realTimeServiceProvider);
  return realTimeService.connectionStream;
}

/// Provides database health status.
///
/// This provider performs basic health checks on the database
/// and returns overall health status information.
@riverpod
Future<Map<String, dynamic>> databaseHealth(DatabaseHealthRef ref) async {
  final repository = ref.watch(webDatabaseRepositoryProvider);
  
  try {
    // Perform a simple query to test database connectivity
    final result = await repository.query('users');
    
    return result.when(
      success: (data) => {
        'status': 'healthy',
        'connected': true,
        'lastChecked': DateTime.now().toIso8601String(),
        'responseTime': 'fast', // Could be measured
      },
      failure: (error) => {
        'status': 'unhealthy',
        'connected': false,
        'lastChecked': DateTime.now().toIso8601String(),
        'error': error.technicalMessage,
      },
    );
  } catch (e) {
    return {
      'status': 'error',
      'connected': false,
      'lastChecked': DateTime.now().toIso8601String(),
      'error': e.toString(),
    };
  }
}

/// Provides active real-time subscriptions count.
///
/// This provider returns the number of active real-time subscriptions
/// for monitoring and debugging purposes.
@riverpod
int activeSubscriptionsCount(ActiveSubscriptionsCountRef ref) {
  final realTimeService = ref.watch(realTimeServiceProvider);
  return realTimeService.subscriptionCount;
}

/// Provides a list of active subscription channel names.
///
/// This provider returns the names of all active real-time channels
/// for debugging and monitoring subscription management.
@riverpod
List<String> activeSubscriptionChannels(ActiveSubscriptionChannelsRef ref) {
  final realTimeService = ref.watch(realTimeServiceProvider);
  return realTimeService.activeChannels;
}

/// Provider for managing database table subscriptions.
///
/// This provider helps manage real-time subscriptions to specific tables
/// with automatic cleanup and error handling.
@riverpod
class DatabaseSubscriptionManager extends _$DatabaseSubscriptionManager {
  @override
  Map<String, bool> build() {
    return {};
  }

  /// Subscribes to a table and tracks the subscription.
  Future<Result<bool, AppError>> subscribeToTable(
    String tableName,
    void Function(PostgresChangePayload) callback,
  ) async {
    final realTimeService = ref.read(realTimeServiceProvider);
    
    final result = await realTimeService.subscribeToTable(tableName, callback);
    
    return result.when(
      success: (channel) {
        state = {...state, tableName: true};
        return const Result.success(true);
      },
      failure: (error) => Result.failure(error),
    );
  }

  /// Unsubscribes from a table and updates tracking.
  Future<Result<bool, AppError>> unsubscribeFromTable(String tableName) async {
    final realTimeService = ref.read(realTimeServiceProvider);
    
    final result = await realTimeService.unsubscribe('public:$tableName');
    
    return result.when(
      success: (success) {
        final newState = Map<String, bool>.from(state);
        newState.remove(tableName);
        state = newState;
        return Result.success(success);
      },
      failure: (error) => Result.failure(error),
    );
  }

  /// Unsubscribes from all tables and clears tracking.
  Future<Result<bool, AppError>> unsubscribeFromAll() async {
    final realTimeService = ref.read(realTimeServiceProvider);
    
    final result = await realTimeService.unsubscribeAll();
    
    return result.when(
      success: (success) {
        state = {};
        return Result.success(success);
      },
      failure: (error) => Result.failure(error),
    );
  }

  /// Gets the list of subscribed table names.
  List<String> get subscribedTables => state.keys.toList();

  /// Checks if a table is currently subscribed.
  bool isSubscribedTo(String tableName) => state[tableName] ?? false;
}

/// Provider for cache management operations.
///
/// This provider provides methods for cache operations like clearing,
/// evicting expired entries, and getting cache statistics.
@riverpod
class CacheManager extends _$CacheManager {
  @override
  Map<String, dynamic> build() {
    return {
      'lastCleared': null,
      'lastEviction': null,
      'operationsCount': 0,
    };
  }

  /// Clears all cache entries.
  Future<Result<bool, AppError>> clearCache() async {
    final cacheService = ref.read(cacheServiceProvider);
    
    final result = await cacheService.clear();
    
    return result.when(
      success: (success) {
        state = {
          ...state,
          'lastCleared': DateTime.now().toIso8601String(),
          'operationsCount': state['operationsCount'] + 1,
        };
        return Result.success(success);
      },
      failure: (error) => Result.failure(error),
    );
  }

  /// Evicts expired cache entries.
  Future<Result<int, AppError>> evictExpiredEntries() async {
    final cacheService = ref.read(cacheServiceProvider);
    
    final result = await cacheService.evictExpired();
    
    return result.when(
      success: (evictedCount) {
        state = {
          ...state,
          'lastEviction': DateTime.now().toIso8601String(),
          'operationsCount': state['operationsCount'] + 1,
        };
        return Result.success(evictedCount);
      },
      failure: (error) => Result.failure(error),
    );
  }

  /// Gets cache statistics.
  Future<Result<Map<String, dynamic>, AppError>> getCacheStatistics() async {
    final cacheService = ref.read(cacheServiceProvider);
    return await cacheService.getStatus();
  }
}
