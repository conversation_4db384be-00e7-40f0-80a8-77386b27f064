import 'package:meditatingleo_webapp/features/database/data/services/web_database_service.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

/// Repository for web database operations following the repository pattern.
///
/// This repository provides a clean interface for database operations,
/// abstracting away the underlying database service implementation and
/// providing consistent error handling and data transformation.
class WebDatabaseRepository {
  final WebDatabaseService _databaseService;

  WebDatabaseRepository(this._databaseService);

  /// Initializes the database repository.
  ///
  /// Returns a [Result] indicating success or failure of initialization.
  Future<Result<bool, AppError>> initialize() async {
    return await _databaseService.initialize();
  }

  /// Executes a query on the specified table.
  ///
  /// [table] - The name of the table to query
  /// Returns a [Result] containing the query results or an error.
  Future<Result<List<Map<String, dynamic>>, AppError>> query(String table) async {
    return await _databaseService.query(table);
  }

  /// Inserts data into the specified table.
  ///
  /// [table] - The name of the table to insert into
  /// [data] - The data to insert
  /// Returns a [Result] containing the inserted data or an error.
  Future<Result<Map<String, dynamic>, AppError>> insert(
    String table,
    Map<String, dynamic> data,
  ) async {
    return await _databaseService.insert(table, data);
  }

  /// Updates data in the specified table.
  ///
  /// [table] - The name of the table to update
  /// [id] - The ID of the record to update
  /// [data] - The data to update
  /// Returns a [Result] containing the updated data or an error.
  Future<Result<Map<String, dynamic>, AppError>> update(
    String table,
    dynamic id,
    Map<String, dynamic> data,
  ) async {
    return await _databaseService.update(table, id, data);
  }

  /// Deletes data from the specified table.
  ///
  /// [table] - The name of the table to delete from
  /// [id] - The ID of the record to delete
  /// Returns a [Result] indicating success or failure.
  Future<Result<bool, AppError>> delete(String table, dynamic id) async {
    return await _databaseService.delete(table, id);
  }

  /// Executes a query with filters and options.
  ///
  /// [table] - The name of the table to query
  /// [filters] - Optional filters to apply
  /// [orderBy] - Optional ordering specification
  /// [limit] - Optional limit on results
  /// Returns a [Result] containing the query results or an error.
  Future<Result<List<Map<String, dynamic>>, AppError>> queryWithFilters(
    String table, {
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    return await _databaseService.queryWithFilters(
      table,
      filters: filters,
      orderBy: orderBy,
      ascending: ascending,
      limit: limit,
    );
  }

  /// Performs a batch insert operation.
  ///
  /// [table] - The name of the table to insert into
  /// [dataList] - List of data objects to insert
  /// Returns a [Result] containing the inserted data or an error.
  Future<Result<List<Map<String, dynamic>>, AppError>> batchInsert(
    String table,
    List<Map<String, dynamic>> dataList,
  ) async {
    return await _databaseService.batchInsert(table, dataList);
  }

  /// Gets data by ID from the specified table.
  ///
  /// [table] - The name of the table to query
  /// [id] - The ID of the record to retrieve
  /// Returns a [Result] containing the record or an error.
  Future<Result<Map<String, dynamic>?, AppError>> getById(
    String table,
    dynamic id,
  ) async {
    try {
      final result = await _databaseService.queryWithFilters(
        table,
        filters: {'id': id},
        limit: 1,
      );

      return result.mapData((data) => data.isNotEmpty ? data.first : null);
    } catch (e) {
      return Result.failure(AppError.database('Failed to get record by ID: $e'));
    }
  }

  /// Checks if a record exists in the specified table.
  ///
  /// [table] - The name of the table to check
  /// [id] - The ID of the record to check
  /// Returns a [Result] indicating whether the record exists.
  Future<Result<bool, AppError>> exists(String table, dynamic id) async {
    final result = await getById(table, id);
    return result.mapData((data) => data != null);
  }

  /// Counts the number of records in the specified table.
  ///
  /// [table] - The name of the table to count
  /// [filters] - Optional filters to apply
  /// Returns a [Result] containing the count or an error.
  Future<Result<int, AppError>> count(
    String table, {
    Map<String, dynamic>? filters,
  }) async {
    final result = await queryWithFilters(table, filters: filters);
    return result.mapData((data) => data.length);
  }
}
