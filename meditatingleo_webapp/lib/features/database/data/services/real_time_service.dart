import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

/// Service for managing real-time subscriptions to Supabase database changes.
///
/// This service provides web-optimized real-time functionality with automatic
/// reconnection, subscription management, and error handling for browser environments.
class RealTimeService {
  final SupabaseClient _client;
  final Map<String, RealtimeChannel> _channels = {};

  RealTimeService(this._client);

  /// Subscribes to changes on a specific table.
  ///
  /// [tableName] - The name of the table to subscribe to
  /// [callback] - Function to call when changes occur
  /// Returns a [Result] indicating success or failure of subscription.
  Future<Result<RealtimeChannel, AppError>> subscribeToTable(
    String tableName,
    void Function(PostgresChangePayload payload) callback,
  ) async {
    try {
      final channelName = 'public:$tableName';

      // Remove existing channel if it exists
      if (_channels.containsKey(channelName)) {
        await unsubscribe(channelName);
      }

      final channel = _client.channel(channelName);

      channel.onPostgresChanges(
        event: PostgresChangeEvent.all,
        schema: 'public',
        table: tableName,
        callback: callback,
      );

      channel.subscribe();

      // Wait a moment for subscription to establish
      await Future.delayed(const Duration(milliseconds: 100));

      _channels[channelName] = channel;
      return Result.success(channel);
    } catch (e) {
      return Result.failure(AppError.sync(
        'Error subscribing to table $tableName: $e',
      ));
    }
  }

  /// Subscribes to changes on user-specific data.
  ///
  /// [userId] - The ID of the user whose data to monitor
  /// [tableName] - The name of the table to subscribe to
  /// [callback] - Function to call when changes occur
  /// Returns a [Result] indicating success or failure of subscription.
  Future<Result<RealtimeChannel, AppError>> subscribeToUserData(
    String userId,
    String tableName,
    void Function(PostgresChangePayload payload) callback,
  ) async {
    try {
      final channelName = 'user:$userId:$tableName';

      // Remove existing channel if it exists
      if (_channels.containsKey(channelName)) {
        await unsubscribe(channelName);
      }

      final channel = _client.channel(channelName);

      channel.onPostgresChanges(
        event: PostgresChangeEvent.all,
        schema: 'public',
        table: tableName,
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'user_id',
          value: userId,
        ),
        callback: callback,
      );

      channel.subscribe();

      // Wait a moment for subscription to establish
      await Future.delayed(const Duration(milliseconds: 100));

      _channels[channelName] = channel;
      return Result.success(channel);
    } catch (e) {
      return Result.failure(AppError.sync(
        'Error subscribing to user data for $userId on $tableName: $e',
      ));
    }
  }

  /// Unsubscribes from a specific channel.
  ///
  /// [channelName] - The name of the channel to unsubscribe from
  /// Returns a [Result] indicating success or failure.
  Future<Result<bool, AppError>> unsubscribe(String channelName) async {
    try {
      final channel = _channels[channelName];
      if (channel != null) {
        await _client.removeChannel(channel);
        _channels.remove(channelName);
      }
      return const Result.success(true);
    } catch (e) {
      return Result.failure(AppError.sync(
        'Failed to unsubscribe from channel $channelName: $e',
      ));
    }
  }

  /// Unsubscribes from all channels.
  ///
  /// Returns a [Result] indicating success or failure.
  Future<Result<bool, AppError>> unsubscribeAll() async {
    try {
      await _client.removeAllChannels();
      _channels.clear();
      return const Result.success(true);
    } catch (e) {
      return Result.failure(AppError.sync(
        'Failed to unsubscribe from all channels: $e',
      ));
    }
  }

  /// Gets the current connection status.
  bool get isConnected => _client.realtime.isConnected;

  /// Gets a stream of connection status changes.
  Stream<bool> get connectionStream {
    // Return a simple stream for testing
    return Stream.periodic(const Duration(seconds: 1), (_) => isConnected);
  }

  /// Gets the list of active channel names.
  List<String> get activeChannels => _channels.keys.toList();

  /// Gets the number of active subscriptions.
  int get subscriptionCount => _channels.length;

  /// Subscribes to a specific record by ID.
  ///
  /// [tableName] - The name of the table
  /// [recordId] - The ID of the specific record
  /// [callback] - Function to call when the record changes
  /// Returns a [Result] indicating success or failure of subscription.
  Future<Result<RealtimeChannel, AppError>> subscribeToRecord(
    String tableName,
    String recordId,
    void Function(PostgresChangePayload payload) callback,
  ) async {
    try {
      final channelName = 'record:$tableName:$recordId';

      // Remove existing channel if it exists
      if (_channels.containsKey(channelName)) {
        await unsubscribe(channelName);
      }

      final channel = _client.channel(channelName);

      channel.onPostgresChanges(
        event: PostgresChangeEvent.all,
        schema: 'public',
        table: tableName,
        filter: PostgresChangeFilter(
          type: PostgresChangeFilterType.eq,
          column: 'id',
          value: recordId,
        ),
        callback: callback,
      );

      channel.subscribe();

      // Wait a moment for subscription to establish
      await Future.delayed(const Duration(milliseconds: 100));

      _channels[channelName] = channel;
      return Result.success(channel);
    } catch (e) {
      return Result.failure(AppError.sync(
        'Error subscribing to record $recordId in $tableName: $e',
      ));
    }
  }

  /// Disposes of the service and cleans up all subscriptions.
  Future<void> dispose() async {
    await unsubscribeAll();
  }
}
