import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

/// Web-specific database service for handling Supabase operations.
///
/// This service provides a web-optimized interface for database operations
/// with enhanced error handling, caching, and performance optimizations
/// specific to web browsers.
class WebDatabaseService {
  final SupabaseClient _client;

  WebDatabaseService(this._client);

  /// Initializes the database service and performs any necessary setup.
  ///
  /// Returns a [Result] indicating success or failure of initialization.
  Future<Result<bool, AppError>> initialize() async {
    try {
      // Perform any initialization logic here
      // For now, just verify the client is available
      if (_client.auth.currentUser != null ||
          _client.auth.currentSession != null) {
        return const Result.success(true);
      }

      // Even if no user is logged in, the service can be initialized
      return const Result.success(true);
    } catch (e) {
      return Result.failure(
          AppError.database('Failed to initialize database service: $e'));
    }
  }

  /// Executes a query on the specified table.
  ///
  /// [table] - The name of the table to query
  /// Returns a [Result] containing the query results or an error.
  Future<Result<List<Map<String, dynamic>>, AppError>> query(
      String table) async {
    try {
      final response = await _client
          .from(table)
          .select()
          .order('created_at', ascending: false);

      return Result.success(List<Map<String, dynamic>>.from(response));
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database('Query failed: ${e.message}'));
    } catch (e) {
      return Result.failure(AppError.database('Unexpected query error: $e'));
    }
  }

  /// Inserts data into the specified table.
  ///
  /// [table] - The name of the table to insert into
  /// [data] - The data to insert
  /// Returns a [Result] containing the inserted data or an error.
  Future<Result<Map<String, dynamic>, AppError>> insert(
    String table,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await _client.from(table).insert(data).select().single();

      return Result.success(Map<String, dynamic>.from(response));
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database('Insert failed: ${e.message}'));
    } catch (e) {
      return Result.failure(AppError.database('Unexpected insert error: $e'));
    }
  }

  /// Updates data in the specified table.
  ///
  /// [table] - The name of the table to update
  /// [id] - The ID of the record to update
  /// [data] - The data to update
  /// Returns a [Result] containing the updated data or an error.
  Future<Result<Map<String, dynamic>, AppError>> update(
    String table,
    dynamic id,
    Map<String, dynamic> data,
  ) async {
    try {
      final response =
          await _client.from(table).update(data).eq('id', id).select().single();

      return Result.success(Map<String, dynamic>.from(response));
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database('Update failed: ${e.message}'));
    } catch (e) {
      return Result.failure(AppError.database('Unexpected update error: $e'));
    }
  }

  /// Deletes data from the specified table.
  ///
  /// [table] - The name of the table to delete from
  /// [id] - The ID of the record to delete
  /// Returns a [Result] indicating success or failure.
  Future<Result<bool, AppError>> delete(String table, dynamic id) async {
    try {
      await _client.from(table).delete().eq('id', id);

      return const Result.success(true);
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database('Delete failed: ${e.message}'));
    } catch (e) {
      return Result.failure(AppError.database('Unexpected delete error: $e'));
    }
  }

  /// Executes a custom query with filters and options.
  ///
  /// [table] - The name of the table to query
  /// [filters] - Optional filters to apply
  /// [orderBy] - Optional ordering specification
  /// [limit] - Optional limit on results
  /// Returns a [Result] containing the query results or an error.
  Future<Result<List<Map<String, dynamic>>, AppError>> queryWithFilters(
    String table, {
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    try {
      dynamic queryBuilder = _client.from(table).select();

      // Apply filters
      if (filters != null) {
        for (final entry in filters.entries) {
          queryBuilder = queryBuilder.eq(entry.key, entry.value);
        }
      }

      // Apply ordering
      if (orderBy != null) {
        queryBuilder = queryBuilder.order(orderBy, ascending: ascending);
      }

      // Apply limit
      if (limit != null) {
        queryBuilder = queryBuilder.limit(limit);
      }

      final response = await queryBuilder;
      return Result.success(List<Map<String, dynamic>>.from(response));
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Filtered query failed: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected filtered query error: $e'));
    }
  }

  /// Performs a batch insert operation.
  ///
  /// [table] - The name of the table to insert into
  /// [dataList] - List of data objects to insert
  /// Returns a [Result] containing the inserted data or an error.
  Future<Result<List<Map<String, dynamic>>, AppError>> batchInsert(
    String table,
    List<Map<String, dynamic>> dataList,
  ) async {
    try {
      final response = await _client.from(table).insert(dataList).select();

      return Result.success(List<Map<String, dynamic>>.from(response));
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Batch insert failed: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected batch insert error: $e'));
    }
  }

  /// Gets the current Supabase client for advanced operations.
  SupabaseClient get client => _client;
}
