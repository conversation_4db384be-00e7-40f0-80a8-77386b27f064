import 'dart:async';
import 'dart:convert';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

/// Simple cache service for testing and development.
class CacheService {
  static const String _keyPrefix = 'meditatingleo_cache_';
  static const String _metadataKey = '${_keyPrefix}metadata';
  static const int _defaultTtlSeconds = 3600; // 1 hour
  static const int _maxCacheSize = 50 * 1024 * 1024; // 50MB

  // Mock storage for testing
  final Map<String, String> _mockStorage = {};

  /// Stores data in the cache with optional TTL.
  Future<Result<bool, AppError>> set(
    String key,
    dynamic data, {
    int? ttlSeconds,
    bool useSessionStorage = false,
  }) async {
    try {
      final cacheKey = _keyPrefix + key;
      final expiresAt = DateTime.now().add(
        Duration(seconds: ttlSeconds ?? _defaultTtlSeconds),
      );

      final cacheEntry = {
        'data': data,
        'expiresAt': expiresAt.millisecondsSinceEpoch,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
      };

      final serializedData = jsonEncode(cacheEntry);
      _mockStorage[cacheKey] = serializedData;

      return const Result.success(true);
    } catch (e) {
      return Result.failure(AppError.cache('Failed to set cache entry: $e'));
    }
  }

  /// Retrieves data from the cache.
  Future<Result<T?, AppError>> get<T>(
    String key, {
    bool useSessionStorage = false,
  }) async {
    try {
      final cacheKey = _keyPrefix + key;
      final serializedData = _mockStorage[cacheKey];

      if (serializedData == null) {
        return const Result.success(null);
      }

      final cacheEntry = jsonDecode(serializedData) as Map<String, dynamic>;
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
        cacheEntry['expiresAt'] as int,
      );

      // Check if entry has expired
      if (DateTime.now().isAfter(expiresAt)) {
        await remove(key, useSessionStorage: useSessionStorage);
        return const Result.success(null);
      }

      return Result.success(cacheEntry['data'] as T?);
    } catch (e) {
      return Result.failure(AppError.cache('Failed to get cache entry: $e'));
    }
  }

  /// Removes a specific entry from the cache.
  Future<Result<bool, AppError>> remove(
    String key, {
    bool useSessionStorage = false,
  }) async {
    try {
      final cacheKey = _keyPrefix + key;
      _mockStorage.remove(cacheKey);
      return const Result.success(true);
    } catch (e) {
      return Result.failure(AppError.cache('Failed to remove cache entry: $e'));
    }
  }

  /// Clears all cache entries.
  Future<Result<bool, AppError>> clear({bool useSessionStorage = false}) async {
    try {
      final keysToRemove = _mockStorage.keys
          .where((key) => key.startsWith(_keyPrefix))
          .toList();

      for (final key in keysToRemove) {
        _mockStorage.remove(key);
      }

      return const Result.success(true);
    } catch (e) {
      return Result.failure(AppError.cache('Failed to clear cache: $e'));
    }
  }

  /// Checks if a key exists in the cache and is not expired.
  Future<Result<bool, AppError>> exists(
    String key, {
    bool useSessionStorage = false,
  }) async {
    final result = await get(key, useSessionStorage: useSessionStorage);
    return result.mapData((data) => data != null);
  }

  /// Gets cache status and statistics.
  Future<Result<Map<String, dynamic>, AppError>> getStatus() async {
    try {
      final cacheEntries = _mockStorage.keys
          .where((key) => key.startsWith(_keyPrefix))
          .length;

      final totalSize = _mockStorage.values
          .map((value) => utf8.encode(value).length)
          .fold<int>(0, (sum, size) => sum + size);

      return Result.success({
        'size': totalSize,
        'localStorageSize': totalSize,
        'sessionStorageSize': 0,
        'entries': cacheEntries,
        'hitRate': 0.85, // Mock hit rate
        'maxSize': _maxCacheSize,
        'usagePercentage': ((totalSize / _maxCacheSize) * 100).round(),
      });
    } catch (e) {
      return Result.failure(AppError.cache('Failed to get cache status: $e'));
    }
  }

  /// Evicts expired entries from the cache.
  Future<Result<int, AppError>> evictExpired() async {
    try {
      int evictedCount = 0;
      final keysToRemove = <String>[];

      for (final entry in _mockStorage.entries) {
        if (entry.key.startsWith(_keyPrefix) && entry.key != _metadataKey) {
          try {
            final cacheEntry = jsonDecode(entry.value) as Map<String, dynamic>;
            final expiresAt = DateTime.fromMillisecondsSinceEpoch(
              cacheEntry['expiresAt'] as int,
            );
            if (DateTime.now().isAfter(expiresAt)) {
              keysToRemove.add(entry.key);
            }
          } catch (e) {
            // Invalid entry, remove it
            keysToRemove.add(entry.key);
          }
        }
      }

      // Remove expired entries
      for (final key in keysToRemove) {
        _mockStorage.remove(key);
        evictedCount++;
      }

      return Result.success(evictedCount);
    } catch (e) {
      return Result.failure(AppError.cache('Failed to evict expired entries: $e'));
    }
  }
}
