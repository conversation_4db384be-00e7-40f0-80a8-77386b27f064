// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'journal_entry_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$JournalEntryModelImpl _$$JournalEntryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$JournalEntryModelImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      journeyId: json['journeyId'] as String?,
      promptId: json['promptId'] as String?,
      moodRating: (json['moodRating'] as num?)?.toInt(),
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      isFavorite: json['isFavorite'] as bool? ?? false,
      isPrivate: json['isPrivate'] as bool? ?? true,
      wordCount: (json['wordCount'] as num?)?.toInt(),
      readingTimeMinutes: (json['readingTimeMinutes'] as num?)?.toInt(),
      syncStatus:
          $enumDecodeNullable(_$SyncStatusEnumMap, json['syncStatus']) ??
              SyncStatus.synced,
      lastSyncAt: json['lastSyncAt'] == null
          ? null
          : DateTime.parse(json['lastSyncAt'] as String),
      version: (json['version'] as num?)?.toInt() ?? 1,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$JournalEntryModelImplToJson(
        _$JournalEntryModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'title': instance.title,
      'content': instance.content,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'journeyId': instance.journeyId,
      'promptId': instance.promptId,
      'moodRating': instance.moodRating,
      'tags': instance.tags,
      'isFavorite': instance.isFavorite,
      'isPrivate': instance.isPrivate,
      'wordCount': instance.wordCount,
      'readingTimeMinutes': instance.readingTimeMinutes,
      'syncStatus': _$SyncStatusEnumMap[instance.syncStatus]!,
      'lastSyncAt': instance.lastSyncAt?.toIso8601String(),
      'version': instance.version,
      'metadata': instance.metadata,
    };

const _$SyncStatusEnumMap = {
  SyncStatus.synced: 'synced',
  SyncStatus.pending: 'pending',
  SyncStatus.syncing: 'syncing',
  SyncStatus.failed: 'failed',
  SyncStatus.conflict: 'conflict',
};
