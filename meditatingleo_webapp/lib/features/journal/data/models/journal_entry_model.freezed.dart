// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'journal_entry_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

JournalEntryModel _$JournalEntryModelFromJson(Map<String, dynamic> json) {
  return _JournalEntryModel.fromJson(json);
}

/// @nodoc
mixin _$JournalEntryModel {
  /// Unique identifier for the journal entry
  String get id => throw _privateConstructorUsedError;

  /// ID of the user who created this entry
  String get userId => throw _privateConstructorUsedError;

  /// Title of the journal entry
  String get title => throw _privateConstructorUsedError;

  /// Main content of the journal entry
  String get content => throw _privateConstructorUsedError;

  /// When the entry was created
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// When the entry was last updated
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Optional journey ID if this entry is part of a guided journey
  String? get journeyId => throw _privateConstructorUsedError;

  /// Optional prompt ID if this entry was created from a specific prompt
  String? get promptId => throw _privateConstructorUsedError;

  /// Mood rating (1-10) associated with this entry
  int? get moodRating => throw _privateConstructorUsedError;

  /// Tags associated with this entry
  List<String> get tags => throw _privateConstructorUsedError;

  /// Whether this entry is marked as favorite
  bool get isFavorite => throw _privateConstructorUsedError;

  /// Whether this entry is private (not shared)
  bool get isPrivate => throw _privateConstructorUsedError;

  /// Word count of the content
  int? get wordCount => throw _privateConstructorUsedError;

  /// Estimated reading time in minutes
  int? get readingTimeMinutes => throw _privateConstructorUsedError;

  /// Sync status for offline-first functionality
  SyncStatus get syncStatus => throw _privateConstructorUsedError;

  /// Last sync timestamp
  DateTime? get lastSyncAt => throw _privateConstructorUsedError;

  /// Version number for conflict resolution
  int get version => throw _privateConstructorUsedError;

  /// Additional metadata as key-value pairs
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// Serializes this JournalEntryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JournalEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JournalEntryModelCopyWith<JournalEntryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JournalEntryModelCopyWith<$Res> {
  factory $JournalEntryModelCopyWith(
          JournalEntryModel value, $Res Function(JournalEntryModel) then) =
      _$JournalEntryModelCopyWithImpl<$Res, JournalEntryModel>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String title,
      String content,
      DateTime createdAt,
      DateTime updatedAt,
      String? journeyId,
      String? promptId,
      int? moodRating,
      List<String> tags,
      bool isFavorite,
      bool isPrivate,
      int? wordCount,
      int? readingTimeMinutes,
      SyncStatus syncStatus,
      DateTime? lastSyncAt,
      int version,
      Map<String, dynamic> metadata});
}

/// @nodoc
class _$JournalEntryModelCopyWithImpl<$Res, $Val extends JournalEntryModel>
    implements $JournalEntryModelCopyWith<$Res> {
  _$JournalEntryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JournalEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? title = null,
    Object? content = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? journeyId = freezed,
    Object? promptId = freezed,
    Object? moodRating = freezed,
    Object? tags = null,
    Object? isFavorite = null,
    Object? isPrivate = null,
    Object? wordCount = freezed,
    Object? readingTimeMinutes = freezed,
    Object? syncStatus = null,
    Object? lastSyncAt = freezed,
    Object? version = null,
    Object? metadata = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      journeyId: freezed == journeyId
          ? _value.journeyId
          : journeyId // ignore: cast_nullable_to_non_nullable
              as String?,
      promptId: freezed == promptId
          ? _value.promptId
          : promptId // ignore: cast_nullable_to_non_nullable
              as String?,
      moodRating: freezed == moodRating
          ? _value.moodRating
          : moodRating // ignore: cast_nullable_to_non_nullable
              as int?,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isFavorite: null == isFavorite
          ? _value.isFavorite
          : isFavorite // ignore: cast_nullable_to_non_nullable
              as bool,
      isPrivate: null == isPrivate
          ? _value.isPrivate
          : isPrivate // ignore: cast_nullable_to_non_nullable
              as bool,
      wordCount: freezed == wordCount
          ? _value.wordCount
          : wordCount // ignore: cast_nullable_to_non_nullable
              as int?,
      readingTimeMinutes: freezed == readingTimeMinutes
          ? _value.readingTimeMinutes
          : readingTimeMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      syncStatus: null == syncStatus
          ? _value.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
      lastSyncAt: freezed == lastSyncAt
          ? _value.lastSyncAt
          : lastSyncAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      metadata: null == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JournalEntryModelImplCopyWith<$Res>
    implements $JournalEntryModelCopyWith<$Res> {
  factory _$$JournalEntryModelImplCopyWith(_$JournalEntryModelImpl value,
          $Res Function(_$JournalEntryModelImpl) then) =
      __$$JournalEntryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String title,
      String content,
      DateTime createdAt,
      DateTime updatedAt,
      String? journeyId,
      String? promptId,
      int? moodRating,
      List<String> tags,
      bool isFavorite,
      bool isPrivate,
      int? wordCount,
      int? readingTimeMinutes,
      SyncStatus syncStatus,
      DateTime? lastSyncAt,
      int version,
      Map<String, dynamic> metadata});
}

/// @nodoc
class __$$JournalEntryModelImplCopyWithImpl<$Res>
    extends _$JournalEntryModelCopyWithImpl<$Res, _$JournalEntryModelImpl>
    implements _$$JournalEntryModelImplCopyWith<$Res> {
  __$$JournalEntryModelImplCopyWithImpl(_$JournalEntryModelImpl _value,
      $Res Function(_$JournalEntryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of JournalEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? title = null,
    Object? content = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? journeyId = freezed,
    Object? promptId = freezed,
    Object? moodRating = freezed,
    Object? tags = null,
    Object? isFavorite = null,
    Object? isPrivate = null,
    Object? wordCount = freezed,
    Object? readingTimeMinutes = freezed,
    Object? syncStatus = null,
    Object? lastSyncAt = freezed,
    Object? version = null,
    Object? metadata = null,
  }) {
    return _then(_$JournalEntryModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      journeyId: freezed == journeyId
          ? _value.journeyId
          : journeyId // ignore: cast_nullable_to_non_nullable
              as String?,
      promptId: freezed == promptId
          ? _value.promptId
          : promptId // ignore: cast_nullable_to_non_nullable
              as String?,
      moodRating: freezed == moodRating
          ? _value.moodRating
          : moodRating // ignore: cast_nullable_to_non_nullable
              as int?,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isFavorite: null == isFavorite
          ? _value.isFavorite
          : isFavorite // ignore: cast_nullable_to_non_nullable
              as bool,
      isPrivate: null == isPrivate
          ? _value.isPrivate
          : isPrivate // ignore: cast_nullable_to_non_nullable
              as bool,
      wordCount: freezed == wordCount
          ? _value.wordCount
          : wordCount // ignore: cast_nullable_to_non_nullable
              as int?,
      readingTimeMinutes: freezed == readingTimeMinutes
          ? _value.readingTimeMinutes
          : readingTimeMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      syncStatus: null == syncStatus
          ? _value.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
      lastSyncAt: freezed == lastSyncAt
          ? _value.lastSyncAt
          : lastSyncAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      metadata: null == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JournalEntryModelImpl implements _JournalEntryModel {
  const _$JournalEntryModelImpl(
      {required this.id,
      required this.userId,
      required this.title,
      required this.content,
      required this.createdAt,
      required this.updatedAt,
      this.journeyId,
      this.promptId,
      this.moodRating,
      final List<String> tags = const [],
      this.isFavorite = false,
      this.isPrivate = true,
      this.wordCount,
      this.readingTimeMinutes,
      this.syncStatus = SyncStatus.synced,
      this.lastSyncAt,
      this.version = 1,
      final Map<String, dynamic> metadata = const {}})
      : _tags = tags,
        _metadata = metadata;

  factory _$JournalEntryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$JournalEntryModelImplFromJson(json);

  /// Unique identifier for the journal entry
  @override
  final String id;

  /// ID of the user who created this entry
  @override
  final String userId;

  /// Title of the journal entry
  @override
  final String title;

  /// Main content of the journal entry
  @override
  final String content;

  /// When the entry was created
  @override
  final DateTime createdAt;

  /// When the entry was last updated
  @override
  final DateTime updatedAt;

  /// Optional journey ID if this entry is part of a guided journey
  @override
  final String? journeyId;

  /// Optional prompt ID if this entry was created from a specific prompt
  @override
  final String? promptId;

  /// Mood rating (1-10) associated with this entry
  @override
  final int? moodRating;

  /// Tags associated with this entry
  final List<String> _tags;

  /// Tags associated with this entry
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  /// Whether this entry is marked as favorite
  @override
  @JsonKey()
  final bool isFavorite;

  /// Whether this entry is private (not shared)
  @override
  @JsonKey()
  final bool isPrivate;

  /// Word count of the content
  @override
  final int? wordCount;

  /// Estimated reading time in minutes
  @override
  final int? readingTimeMinutes;

  /// Sync status for offline-first functionality
  @override
  @JsonKey()
  final SyncStatus syncStatus;

  /// Last sync timestamp
  @override
  final DateTime? lastSyncAt;

  /// Version number for conflict resolution
  @override
  @JsonKey()
  final int version;

  /// Additional metadata as key-value pairs
  final Map<String, dynamic> _metadata;

  /// Additional metadata as key-value pairs
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString() {
    return 'JournalEntryModel(id: $id, userId: $userId, title: $title, content: $content, createdAt: $createdAt, updatedAt: $updatedAt, journeyId: $journeyId, promptId: $promptId, moodRating: $moodRating, tags: $tags, isFavorite: $isFavorite, isPrivate: $isPrivate, wordCount: $wordCount, readingTimeMinutes: $readingTimeMinutes, syncStatus: $syncStatus, lastSyncAt: $lastSyncAt, version: $version, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JournalEntryModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.journeyId, journeyId) ||
                other.journeyId == journeyId) &&
            (identical(other.promptId, promptId) ||
                other.promptId == promptId) &&
            (identical(other.moodRating, moodRating) ||
                other.moodRating == moodRating) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.isFavorite, isFavorite) ||
                other.isFavorite == isFavorite) &&
            (identical(other.isPrivate, isPrivate) ||
                other.isPrivate == isPrivate) &&
            (identical(other.wordCount, wordCount) ||
                other.wordCount == wordCount) &&
            (identical(other.readingTimeMinutes, readingTimeMinutes) ||
                other.readingTimeMinutes == readingTimeMinutes) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus) &&
            (identical(other.lastSyncAt, lastSyncAt) ||
                other.lastSyncAt == lastSyncAt) &&
            (identical(other.version, version) || other.version == version) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      title,
      content,
      createdAt,
      updatedAt,
      journeyId,
      promptId,
      moodRating,
      const DeepCollectionEquality().hash(_tags),
      isFavorite,
      isPrivate,
      wordCount,
      readingTimeMinutes,
      syncStatus,
      lastSyncAt,
      version,
      const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of JournalEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JournalEntryModelImplCopyWith<_$JournalEntryModelImpl> get copyWith =>
      __$$JournalEntryModelImplCopyWithImpl<_$JournalEntryModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$JournalEntryModelImplToJson(
      this,
    );
  }
}

abstract class _JournalEntryModel implements JournalEntryModel {
  const factory _JournalEntryModel(
      {required final String id,
      required final String userId,
      required final String title,
      required final String content,
      required final DateTime createdAt,
      required final DateTime updatedAt,
      final String? journeyId,
      final String? promptId,
      final int? moodRating,
      final List<String> tags,
      final bool isFavorite,
      final bool isPrivate,
      final int? wordCount,
      final int? readingTimeMinutes,
      final SyncStatus syncStatus,
      final DateTime? lastSyncAt,
      final int version,
      final Map<String, dynamic> metadata}) = _$JournalEntryModelImpl;

  factory _JournalEntryModel.fromJson(Map<String, dynamic> json) =
      _$JournalEntryModelImpl.fromJson;

  /// Unique identifier for the journal entry
  @override
  String get id;

  /// ID of the user who created this entry
  @override
  String get userId;

  /// Title of the journal entry
  @override
  String get title;

  /// Main content of the journal entry
  @override
  String get content;

  /// When the entry was created
  @override
  DateTime get createdAt;

  /// When the entry was last updated
  @override
  DateTime get updatedAt;

  /// Optional journey ID if this entry is part of a guided journey
  @override
  String? get journeyId;

  /// Optional prompt ID if this entry was created from a specific prompt
  @override
  String? get promptId;

  /// Mood rating (1-10) associated with this entry
  @override
  int? get moodRating;

  /// Tags associated with this entry
  @override
  List<String> get tags;

  /// Whether this entry is marked as favorite
  @override
  bool get isFavorite;

  /// Whether this entry is private (not shared)
  @override
  bool get isPrivate;

  /// Word count of the content
  @override
  int? get wordCount;

  /// Estimated reading time in minutes
  @override
  int? get readingTimeMinutes;

  /// Sync status for offline-first functionality
  @override
  SyncStatus get syncStatus;

  /// Last sync timestamp
  @override
  DateTime? get lastSyncAt;

  /// Version number for conflict resolution
  @override
  int get version;

  /// Additional metadata as key-value pairs
  @override
  Map<String, dynamic> get metadata;

  /// Create a copy of JournalEntryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JournalEntryModelImplCopyWith<_$JournalEntryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
