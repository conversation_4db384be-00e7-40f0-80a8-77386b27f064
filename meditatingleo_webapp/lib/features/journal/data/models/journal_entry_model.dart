import 'package:freezed_annotation/freezed_annotation.dart';

part 'journal_entry_model.freezed.dart';
part 'journal_entry_model.g.dart';

/// Model representing a journal entry in the web application.
///
/// This model contains all the data for a user's journal entry including
/// content, metadata, and synchronization information for web-specific features.
@freezed
class JournalEntryModel with _$JournalEntryModel {
  /// Creates a [JournalEntryModel] instance.
  const factory JournalEntryModel({
    /// Unique identifier for the journal entry
    required String id,
    
    /// ID of the user who created this entry
    required String userId,
    
    /// Title of the journal entry
    required String title,
    
    /// Main content of the journal entry
    required String content,
    
    /// When the entry was created
    required DateTime createdAt,
    
    /// When the entry was last updated
    required DateTime updatedAt,
    
    /// Optional journey ID if this entry is part of a guided journey
    String? journeyId,
    
    /// Optional prompt ID if this entry was created from a specific prompt
    String? promptId,
    
    /// Mood rating (1-10) associated with this entry
    int? moodRating,
    
    /// Tags associated with this entry
    @Default([]) List<String> tags,
    
    /// Whether this entry is marked as favorite
    @Default(false) bool isFavorite,
    
    /// Whether this entry is private (not shared)
    @Default(true) bool isPrivate,
    
    /// Word count of the content
    int? wordCount,
    
    /// Estimated reading time in minutes
    int? readingTimeMinutes,
    
    /// Sync status for offline-first functionality
    @Default(SyncStatus.synced) SyncStatus syncStatus,
    
    /// Last sync timestamp
    DateTime? lastSyncAt,
    
    /// Version number for conflict resolution
    @Default(1) int version,
    
    /// Additional metadata as key-value pairs
    @Default({}) Map<String, dynamic> metadata,
  }) = _JournalEntryModel;

  /// Creates a [JournalEntryModel] from JSON.
  factory JournalEntryModel.fromJson(Map<String, dynamic> json) =>
      _$JournalEntryModelFromJson(json);
}

/// Enum representing the synchronization status of a journal entry.
enum SyncStatus {
  /// Entry is fully synchronized with the server
  synced,
  
  /// Entry has local changes that need to be synchronized
  pending,
  
  /// Entry is currently being synchronized
  syncing,
  
  /// Entry failed to synchronize
  failed,
  
  /// Entry has conflicts that need to be resolved
  conflict,
}

/// Extension methods for [JournalEntryModel] to provide convenient utilities.
extension JournalEntryModelExtensions on JournalEntryModel {
  /// Returns true if this entry needs synchronization.
  bool get needsSync => syncStatus == SyncStatus.pending || syncStatus == SyncStatus.failed;
  
  /// Returns true if this entry is currently being synchronized.
  bool get isSyncing => syncStatus == SyncStatus.syncing;
  
  /// Returns true if this entry has sync conflicts.
  bool get hasConflicts => syncStatus == SyncStatus.conflict;
  
  /// Returns true if this entry is fully synchronized.
  bool get isSynced => syncStatus == SyncStatus.synced;
  
  /// Returns a copy of this entry with updated sync status.
  JournalEntryModel withSyncStatus(SyncStatus status) {
    return copyWith(
      syncStatus: status,
      lastSyncAt: status == SyncStatus.synced ? DateTime.now() : lastSyncAt,
    );
  }
  
  /// Returns a copy of this entry with updated content and metadata.
  JournalEntryModel withUpdatedContent(String newContent, {String? newTitle}) {
    final now = DateTime.now();
    return copyWith(
      content: newContent,
      title: newTitle ?? title,
      updatedAt: now,
      wordCount: _calculateWordCount(newContent),
      readingTimeMinutes: _calculateReadingTime(newContent),
      syncStatus: SyncStatus.pending,
      version: version + 1,
    );
  }
  
  /// Returns a copy of this entry marked as favorite or unfavorite.
  JournalEntryModel toggleFavorite() {
    return copyWith(
      isFavorite: !isFavorite,
      updatedAt: DateTime.now(),
      syncStatus: SyncStatus.pending,
      version: version + 1,
    );
  }
  
  /// Returns a copy of this entry with updated tags.
  JournalEntryModel withTags(List<String> newTags) {
    return copyWith(
      tags: newTags,
      updatedAt: DateTime.now(),
      syncStatus: SyncStatus.pending,
      version: version + 1,
    );
  }
  
  /// Returns a copy of this entry with updated mood rating.
  JournalEntryModel withMoodRating(int? rating) {
    return copyWith(
      moodRating: rating,
      updatedAt: DateTime.now(),
      syncStatus: SyncStatus.pending,
      version: version + 1,
    );
  }
  
  /// Calculates the word count of the given text.
  static int _calculateWordCount(String text) {
    if (text.trim().isEmpty) return 0;
    return text.trim().split(RegExp(r'\s+')).length;
  }
  
  /// Calculates the estimated reading time in minutes.
  static int _calculateReadingTime(String text) {
    const wordsPerMinute = 200; // Average reading speed
    final wordCount = _calculateWordCount(text);
    return (wordCount / wordsPerMinute).ceil().clamp(1, double.infinity).toInt();
  }
  
  /// Returns a summary of the entry for display purposes.
  String get summary {
    if (content.length <= 100) return content;
    return '${content.substring(0, 97)}...';
  }
  
  /// Returns true if this entry was created today.
  bool get isCreatedToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final entryDate = DateTime(createdAt.year, createdAt.month, createdAt.day);
    return entryDate == today;
  }
  
  /// Returns true if this entry was updated today.
  bool get isUpdatedToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final updateDate = DateTime(updatedAt.year, updatedAt.month, updatedAt.day);
    return updateDate == today;
  }
  
  /// Returns the age of this entry in days.
  int get ageInDays {
    return DateTime.now().difference(createdAt).inDays;
  }
  
  /// Converts this model to a JSON map suitable for Supabase.
  Map<String, dynamic> toSupabaseJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'content': content,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'journey_id': journeyId,
      'prompt_id': promptId,
      'mood_rating': moodRating,
      'tags': tags,
      'is_favorite': isFavorite,
      'is_private': isPrivate,
      'word_count': wordCount,
      'reading_time_minutes': readingTimeMinutes,
      'sync_status': syncStatus.name,
      'last_sync_at': lastSyncAt?.toIso8601String(),
      'version': version,
      'metadata': metadata,
    };
  }
  
  /// Creates a [JournalEntryModel] from Supabase JSON.
  static JournalEntryModel fromSupabaseJson(Map<String, dynamic> json) {
    return JournalEntryModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      journeyId: json['journey_id'] as String?,
      promptId: json['prompt_id'] as String?,
      moodRating: json['mood_rating'] as int?,
      tags: List<String>.from(json['tags'] ?? []),
      isFavorite: json['is_favorite'] as bool? ?? false,
      isPrivate: json['is_private'] as bool? ?? true,
      wordCount: json['word_count'] as int?,
      readingTimeMinutes: json['reading_time_minutes'] as int?,
      syncStatus: SyncStatus.values.firstWhere(
        (status) => status.name == json['sync_status'],
        orElse: () => SyncStatus.synced,
      ),
      lastSyncAt: json['last_sync_at'] != null 
          ? DateTime.parse(json['last_sync_at'] as String)
          : null,
      version: json['version'] as int? ?? 1,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }
}
