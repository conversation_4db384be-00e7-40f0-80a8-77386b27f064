import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_webapp/features/journal/data/models/journal_entry_model.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

/// Service for managing journal entries with web-specific optimizations.
///
/// This service handles all journal-related database operations including
/// CRUD operations, real-time updates, and web-specific caching strategies.
class JournalService {
  final SupabaseClient _client;
  static const String _tableName = 'journal_entries';

  JournalService(this._client);

  /// Helper method to convert Supabase JSON to JournalEntryModel
  JournalEntryModel _fromSupabaseJson(Map<String, dynamic> json) {
    return JournalEntryModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      journeyId: json['journey_id'] as String?,
      promptId: json['prompt_id'] as String?,
      moodRating: json['mood_rating'] as int?,
      tags: List<String>.from(json['tags'] ?? []),
      isFavorite: json['is_favorite'] as bool? ?? false,
      isPrivate: json['is_private'] as bool? ?? true,
      wordCount: json['word_count'] as int?,
      readingTimeMinutes: json['reading_time_minutes'] as int?,
      syncStatus: SyncStatus.values.firstWhere(
        (status) => status.name == json['sync_status'],
        orElse: () => SyncStatus.synced,
      ),
      lastSyncAt: json['last_sync_at'] != null
          ? DateTime.parse(json['last_sync_at'] as String)
          : null,
      version: json['version'] as int? ?? 1,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  /// Creates a new journal entry.
  ///
  /// [entry] - The journal entry to create
  /// Returns a [Result] containing the created entry or an error.
  Future<Result<JournalEntryModel, AppError>> createEntry(
    JournalEntryModel entry,
  ) async {
    try {
      final response = await _client
          .from(_tableName)
          .insert(entry.toSupabaseJson())
          .select()
          .single();

      final createdEntry = _fromSupabaseJson(response);
      return Result.success(createdEntry);
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Failed to create journal entry: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected error creating journal entry: $e'));
    }
  }

  /// Retrieves a journal entry by ID.
  ///
  /// [entryId] - The ID of the entry to retrieve
  /// Returns a [Result] containing the entry or an error.
  Future<Result<JournalEntryModel?, AppError>> getEntry(String entryId) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('id', entryId)
          .maybeSingle();

      if (response == null) {
        return Result.failure(AppError.notFound('Journal entry'));
      }

      final entry = _fromSupabaseJson(response);
      return Result.success(entry);
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Failed to get journal entry: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected error getting journal entry: $e'));
    }
  }

  /// Retrieves all journal entries for a specific user.
  ///
  /// [userId] - The ID of the user whose entries to retrieve
  /// [limit] - Optional limit on the number of entries to retrieve
  /// [offset] - Optional offset for pagination
  /// Returns a [Result] containing the list of entries or an error.
  Future<Result<List<JournalEntryModel>, AppError>> getUserEntries(
    String userId, {
    int? limit,
    int? offset,
  }) async {
    try {
      var query = _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 50) - 1);
      }

      final response = await query;
      final entries = response.map((json) => _fromSupabaseJson(json)).toList();

      return Result.success(entries);
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Failed to get user entries: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected error getting user entries: $e'));
    }
  }

  /// Updates an existing journal entry.
  ///
  /// [entry] - The updated journal entry
  /// Returns a [Result] containing the updated entry or an error.
  Future<Result<JournalEntryModel, AppError>> updateEntry(
    JournalEntryModel entry,
  ) async {
    try {
      final response = await _client
          .from(_tableName)
          .update(entry.toSupabaseJson())
          .eq('id', entry.id)
          .select()
          .single();

      final updatedEntry = _fromSupabaseJson(response);
      return Result.success(updatedEntry);
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Failed to update journal entry: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected error updating journal entry: $e'));
    }
  }

  /// Deletes a journal entry.
  ///
  /// [entryId] - The ID of the entry to delete
  /// Returns a [Result] indicating success or failure.
  Future<Result<bool, AppError>> deleteEntry(String entryId) async {
    try {
      await _client.from(_tableName).delete().eq('id', entryId);

      return const Result.success(true);
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Failed to delete journal entry: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected error deleting journal entry: $e'));
    }
  }

  /// Searches journal entries by content or title.
  ///
  /// [userId] - The ID of the user whose entries to search
  /// [query] - The search query
  /// [limit] - Optional limit on results
  /// Returns a [Result] containing matching entries or an error.
  Future<Result<List<JournalEntryModel>, AppError>> searchEntries(
    String userId,
    String query, {
    int? limit,
  }) async {
    try {
      var searchQuery = _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .or('title.ilike.%$query%,content.ilike.%$query%')
          .order('created_at', ascending: false);

      if (limit != null) {
        searchQuery = searchQuery.limit(limit);
      }

      final response = await searchQuery;
      final entries = response.map((json) => _fromSupabaseJson(json)).toList();

      return Result.success(entries);
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Failed to search entries: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected error searching entries: $e'));
    }
  }

  /// Gets entries by tags.
  ///
  /// [userId] - The ID of the user whose entries to retrieve
  /// [tags] - List of tags to filter by
  /// [limit] - Optional limit on results
  /// Returns a [Result] containing matching entries or an error.
  Future<Result<List<JournalEntryModel>, AppError>> getEntriesByTags(
    String userId,
    List<String> tags, {
    int? limit,
  }) async {
    try {
      var query = _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .contains('tags', tags)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      final entries = response.map((json) => _fromSupabaseJson(json)).toList();

      return Result.success(entries);
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Failed to get entries by tags: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected error getting entries by tags: $e'));
    }
  }

  /// Gets favorite entries for a user.
  ///
  /// [userId] - The ID of the user whose favorites to retrieve
  /// [limit] - Optional limit on results
  /// Returns a [Result] containing favorite entries or an error.
  Future<Result<List<JournalEntryModel>, AppError>> getFavoriteEntries(
    String userId, {
    int? limit,
  }) async {
    try {
      var query = _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .eq('is_favorite', true)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      final entries = response.map((json) => _fromSupabaseJson(json)).toList();

      return Result.success(entries);
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Failed to get favorite entries: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected error getting favorite entries: $e'));
    }
  }

  /// Gets entries for a specific date range.
  ///
  /// [userId] - The ID of the user whose entries to retrieve
  /// [startDate] - Start of the date range
  /// [endDate] - End of the date range
  /// Returns a [Result] containing entries in the date range or an error.
  Future<Result<List<JournalEntryModel>, AppError>> getEntriesInDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final response = await _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .gte('created_at', startDate.toIso8601String())
          .lte('created_at', endDate.toIso8601String())
          .order('created_at', ascending: false);

      final entries = response.map((json) => _fromSupabaseJson(json)).toList();

      return Result.success(entries);
    } on PostgrestException catch (e) {
      return Result.failure(AppError.database(
          'Failed to get entries in date range: ${e.message}'));
    } catch (e) {
      return Result.failure(AppError.database(
          'Unexpected error getting entries in date range: $e'));
    }
  }

  /// Gets entry statistics for a user.
  ///
  /// [userId] - The ID of the user whose statistics to retrieve
  /// Returns a [Result] containing entry statistics or an error.
  Future<Result<Map<String, dynamic>, AppError>> getEntryStatistics(
    String userId,
  ) async {
    try {
      final response = await _client
          .from(_tableName)
          .select('id, word_count, mood_rating, created_at')
          .eq('user_id', userId);

      final totalEntries = response.length;
      final totalWords = response
          .map((entry) => entry['word_count'] as int? ?? 0)
          .fold<int>(0, (sum, count) => sum + count);

      final moodRatings = response
          .map((entry) => entry['mood_rating'] as int?)
          .where((rating) => rating != null)
          .cast<int>()
          .toList();

      final averageMood = moodRatings.isNotEmpty
          ? moodRatings.reduce((a, b) => a + b) / moodRatings.length
          : 0.0;

      final now = DateTime.now();
      final thisMonth = response.where((entry) {
        final createdAt = DateTime.parse(entry['created_at'] as String);
        return createdAt.year == now.year && createdAt.month == now.month;
      }).length;

      return Result.success({
        'totalEntries': totalEntries,
        'totalWords': totalWords,
        'averageMood': averageMood,
        'entriesThisMonth': thisMonth,
        'averageWordsPerEntry':
            totalEntries > 0 ? totalWords / totalEntries : 0,
      });
    } on PostgrestException catch (e) {
      return Result.failure(
          AppError.database('Failed to get entry statistics: ${e.message}'));
    } catch (e) {
      return Result.failure(
          AppError.database('Unexpected error getting entry statistics: $e'));
    }
  }
}
