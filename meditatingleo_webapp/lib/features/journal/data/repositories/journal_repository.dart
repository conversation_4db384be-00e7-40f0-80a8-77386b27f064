import 'package:meditatingleo_webapp/features/journal/data/services/journal_service.dart';
import 'package:meditatingleo_webapp/features/journal/data/models/journal_entry_model.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

/// Repository for managing journal entries following the repository pattern.
///
/// This repository provides a clean interface for journal operations,
/// abstracting away the underlying service implementation and providing
/// consistent error handling and data transformation.
class JournalRepository {
  final JournalService _journalService;

  JournalRepository(this._journalService);

  /// Creates a new journal entry.
  ///
  /// [entry] - The journal entry to create
  /// Returns a [Result] containing the created entry or an error.
  Future<Result<JournalEntryModel, AppError>> createEntry(
    JournalEntryModel entry,
  ) async {
    return await _journalService.createEntry(entry);
  }

  /// Retrieves a journal entry by ID.
  ///
  /// [entryId] - The ID of the entry to retrieve
  /// Returns a [Result] containing the entry or an error.
  Future<Result<JournalEntryModel?, AppError>> getEntry(String entryId) async {
    return await _journalService.getEntry(entryId);
  }

  /// Retrieves all journal entries for a specific user.
  ///
  /// [userId] - The ID of the user whose entries to retrieve
  /// [limit] - Optional limit on the number of entries to retrieve
  /// [offset] - Optional offset for pagination
  /// Returns a [Result] containing the list of entries or an error.
  Future<Result<List<JournalEntryModel>, AppError>> getUserEntries(
    String userId, {
    int? limit,
    int? offset,
  }) async {
    return await _journalService.getUserEntries(
      userId,
      limit: limit,
      offset: offset,
    );
  }

  /// Updates an existing journal entry.
  ///
  /// [entry] - The updated journal entry
  /// Returns a [Result] containing the updated entry or an error.
  Future<Result<JournalEntryModel, AppError>> updateEntry(
    JournalEntryModel entry,
  ) async {
    return await _journalService.updateEntry(entry);
  }

  /// Deletes a journal entry.
  ///
  /// [entryId] - The ID of the entry to delete
  /// Returns a [Result] indicating success or failure.
  Future<Result<bool, AppError>> deleteEntry(String entryId) async {
    return await _journalService.deleteEntry(entryId);
  }

  /// Searches journal entries by content or title.
  ///
  /// [userId] - The ID of the user whose entries to search
  /// [query] - The search query
  /// [limit] - Optional limit on results
  /// Returns a [Result] containing matching entries or an error.
  Future<Result<List<JournalEntryModel>, AppError>> searchEntries(
    String userId,
    String query, {
    int? limit,
  }) async {
    return await _journalService.searchEntries(userId, query, limit: limit);
  }

  /// Gets entries by tags.
  ///
  /// [userId] - The ID of the user whose entries to retrieve
  /// [tags] - List of tags to filter by
  /// [limit] - Optional limit on results
  /// Returns a [Result] containing matching entries or an error.
  Future<Result<List<JournalEntryModel>, AppError>> getEntriesByTags(
    String userId,
    List<String> tags, {
    int? limit,
  }) async {
    return await _journalService.getEntriesByTags(userId, tags, limit: limit);
  }

  /// Gets favorite entries for a user.
  ///
  /// [userId] - The ID of the user whose favorites to retrieve
  /// [limit] - Optional limit on results
  /// Returns a [Result] containing favorite entries or an error.
  Future<Result<List<JournalEntryModel>, AppError>> getFavoriteEntries(
    String userId, {
    int? limit,
  }) async {
    return await _journalService.getFavoriteEntries(userId, limit: limit);
  }

  /// Gets entries for a specific date range.
  ///
  /// [userId] - The ID of the user whose entries to retrieve
  /// [startDate] - Start of the date range
  /// [endDate] - End of the date range
  /// Returns a [Result] containing entries in the date range or an error.
  Future<Result<List<JournalEntryModel>, AppError>> getEntriesInDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    return await _journalService.getEntriesInDateRange(
        userId, startDate, endDate);
  }

  /// Gets entry statistics for a user.
  ///
  /// [userId] - The ID of the user whose statistics to retrieve
  /// Returns a [Result] containing entry statistics or an error.
  Future<Result<Map<String, dynamic>, AppError>> getEntryStatistics(
    String userId,
  ) async {
    return await _journalService.getEntryStatistics(userId);
  }

  /// Gets recent entries for a user (last 30 days).
  ///
  /// [userId] - The ID of the user whose recent entries to retrieve
  /// [limit] - Optional limit on results (default: 10)
  /// Returns a [Result] containing recent entries or an error.
  Future<Result<List<JournalEntryModel>, AppError>> getRecentEntries(
    String userId, {
    int limit = 10,
  }) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(const Duration(days: 30));

    final result = await getEntriesInDateRange(userId, startDate, endDate);

    return result.mapData((entries) {
      // Take only the requested number of entries
      return entries.take(limit).toList();
    });
  }

  /// Toggles the favorite status of an entry.
  ///
  /// [entryId] - The ID of the entry to toggle
  /// Returns a [Result] containing the updated entry or an error.
  Future<Result<JournalEntryModel, AppError>> toggleFavorite(
      String entryId) async {
    final entryResult = await getEntry(entryId);

    return entryResult.flatMapAsync((entry) async {
      if (entry == null) {
        return const Result.failure(AppError.notFound('Journal entry'));
      }

      final updatedEntry = entry.toggleFavorite();
      return await updateEntry(updatedEntry);
    });
  }

  /// Updates the mood rating for an entry.
  ///
  /// [entryId] - The ID of the entry to update
  /// [moodRating] - The new mood rating (1-10)
  /// Returns a [Result] containing the updated entry or an error.
  Future<Result<JournalEntryModel, AppError>> updateMoodRating(
    String entryId,
    int? moodRating,
  ) async {
    final entryResult = await getEntry(entryId);

    return entryResult.flatMapAsync((entry) async {
      if (entry == null) {
        return const Result.failure(AppError.notFound('Journal entry'));
      }

      final updatedEntry = entry.withMoodRating(moodRating);
      return await updateEntry(updatedEntry);
    });
  }

  /// Updates the tags for an entry.
  ///
  /// [entryId] - The ID of the entry to update
  /// [tags] - The new list of tags
  /// Returns a [Result] containing the updated entry or an error.
  Future<Result<JournalEntryModel, AppError>> updateTags(
    String entryId,
    List<String> tags,
  ) async {
    final entryResult = await getEntry(entryId);

    return entryResult.flatMapAsync((entry) async {
      if (entry == null) {
        return const Result.failure(AppError.notFound('Journal entry'));
      }

      final updatedEntry = entry.withTags(tags);
      return await updateEntry(updatedEntry);
    });
  }

  /// Gets all unique tags used by a user.
  ///
  /// [userId] - The ID of the user whose tags to retrieve
  /// Returns a [Result] containing the list of unique tags or an error.
  Future<Result<List<String>, AppError>> getUserTags(String userId) async {
    final entriesResult = await getUserEntries(userId);

    return entriesResult.mapData((entries) {
      final allTags = <String>{};
      for (final entry in entries) {
        allTags.addAll(entry.tags);
      }
      return allTags.toList()..sort();
    });
  }

  /// Creates a new journal entry with auto-generated metadata.
  ///
  /// [userId] - The ID of the user creating the entry
  /// [title] - The title of the entry
  /// [content] - The content of the entry
  /// [journeyId] - Optional journey ID
  /// [promptId] - Optional prompt ID
  /// Returns a [Result] containing the created entry or an error.
  Future<Result<JournalEntryModel, AppError>> createNewEntry(
    String userId,
    String title,
    String content, {
    String? journeyId,
    String? promptId,
  }) async {
    final now = DateTime.now();
    final entry = JournalEntryModel(
      id: '', // Will be generated by Supabase
      userId: userId,
      title: title,
      content: content,
      createdAt: now,
      updatedAt: now,
      journeyId: journeyId,
      promptId: promptId,
      wordCount: content.trim().isEmpty
          ? 0
          : content.trim().split(RegExp(r'\s+')).length,
      readingTimeMinutes: _calculateReadingTime(content),
    );

    return await createEntry(entry);
  }

  /// Calculates the estimated reading time in minutes.
  static int _calculateReadingTime(String text) {
    const wordsPerMinute = 200; // Average reading speed
    final wordCount =
        text.trim().isEmpty ? 0 : text.trim().split(RegExp(r'\s+')).length;
    return (wordCount / wordsPerMinute)
        .ceil()
        .clamp(1, double.infinity)
        .toInt();
  }
}
