# meditatingleo_webapp

# ClarityByMeditatingLeo Web Application

A desktop-optimized Flutter web application for mindfulness journaling and personal growth. This application provides a responsive, Progressive Web App (PWA) experience designed specifically for desktop and tablet users.

## Overview

The ClarityByMeditatingLeo Web Application is part of a three-application ecosystem that includes:
- **Web Application** (this app) - Desktop-optimized experience
- **Mobile Application** - iOS and Android native experience
- **Admin Panel** - Content management and system administration

## Features

### Core Features
- **Responsive Design**: Optimized layouts for desktop, tablet, and mobile screens
- **Material Design 3**: Modern UI following latest Material Design guidelines
- **Progressive Web App**: Installable web app with offline capabilities
- **Modern Flutter 2025+**: Uses latest Flutter APIs and patterns

### Technical Features
- **Riverpod State Management**: Modern state management with code generation
- **GoRouter Navigation**: Declarative routing with deep linking support
- **Supabase Integration**: Backend connectivity for data synchronization
- **Service Worker**: Offline functionality and caching
- **Responsive Layouts**: Adaptive UI for different screen sizes

## Getting Started

### Prerequisites
- Flutter 3.24.0+
- Dart 3.5.3+
- Web browser with modern JavaScript support
- Supabase account (for backend integration)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd meditatingleo_webapp
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run code generation**
   ```bash
   dart run build_runner build
   ```

4. **Configure environment variables**
   Create a `.env` file in the root directory:
   ```env
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

5. **Run the application**
   ```bash
   flutter run -d web-server --web-port 8080
   ```

### Building for Production

```bash
# Build for web deployment
flutter build web

# The built files will be in the build/web directory
```

## Architecture

### Project Structure
```
lib/
├── main.dart                 # Application entry point
├── app/
│   └── app.dart             # Main app widget
├── core/
│   ├── constants/           # App-wide constants
│   ├── theme/              # Theme configuration
│   └── utils/              # Utility functions
├── features/
│   └── home/               # Feature modules
│       └── presentation/
├── shared/
│   ├── providers/          # Global Riverpod providers
│   └── widgets/           # Reusable widgets
```

### Key Technologies
- **Flutter**: Cross-platform UI framework
- **Riverpod**: State management with code generation
- **GoRouter**: Declarative routing
- **Supabase**: Backend as a Service
- **Material Design 3**: Modern design system

## Development Guidelines

### Code Standards
- **File Size Limit**: Maximum 200 lines per file
- **Function Size Limit**: Maximum 50 lines per function
- **Widget Organization**: One widget per file
- **Modern Flutter APIs**: Use Color.withValues(), @riverpod, Material Design 3

### Testing
```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage
```

### Code Generation
```bash
# Generate code for Riverpod, Freezed, and JSON serialization
dart run build_runner build

# Watch for changes and regenerate automatically
dart run build_runner watch
```

## Deployment

### Web Deployment
1. Build the application: `flutter build web`
2. Deploy the `build/web` directory to your web server
3. Configure your server to serve the `index.html` for all routes (SPA routing)

### PWA Features
- **Offline Support**: Basic caching with service worker
- **Installable**: Can be installed as a desktop app
- **Responsive**: Adapts to different screen sizes
- **Fast Loading**: Optimized assets and caching

## Contributing

### Development Workflow
1. Create a feature branch
2. Follow the established coding standards
3. Write comprehensive tests
4. Update documentation
5. Submit a pull request

### Code Quality
- Maintain 80%+ test coverage
- Follow Flutter best practices
- Use modern Flutter 2025+ patterns
- Document all public APIs

## Configuration

### Environment Variables
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_ANON_KEY`: Supabase anonymous key
- `ENVIRONMENT`: Development environment (development/staging/production)

### Build Configuration
- `build.yaml`: Code generation configuration
- `pubspec.yaml`: Dependencies and Flutter configuration
- `web/manifest.json`: PWA configuration

## Browser Support

### Supported Browsers
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Features by Browser
- **Service Worker**: All modern browsers
- **PWA Installation**: Chrome, Edge, Firefox
- **Responsive Design**: All browsers

## Performance

### Optimization Features
- **Tree Shaking**: Unused code elimination
- **Asset Optimization**: Compressed images and fonts
- **Lazy Loading**: On-demand resource loading
- **Caching Strategy**: Intelligent service worker caching

### Performance Targets
- **First Contentful Paint**: < 2 seconds
- **Largest Contentful Paint**: < 3 seconds
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms

## Security

### Security Features
- **HTTPS Only**: Secure communication
- **Content Security Policy**: XSS protection
- **Secure Headers**: Additional security measures
- **Input Validation**: Client-side validation

## License

This project is part of the ClarityByMeditatingLeo ecosystem. See the main project repository for license information.

## Support

For support and questions:
- Check the documentation
- Review the issue tracker
- Contact the development team

---

**Last Updated**: January 2025
**Flutter Version**: 3.24.0+
**Dart Version**: 3.5.3+
