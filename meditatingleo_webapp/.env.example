# ClarityByMeditatingLeo Web Application Environment Configuration
# Copy this file to .env and fill in your actual values

# Supabase Configuration (Required)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Web Application Configuration
WEB_APP_URL=http://localhost:8080
WEB_APP_NAME=ClarityByMeditatingLeo
WEB_APP_VERSION=1.0.0

# Analytics & Monitoring (Optional)
SENTRY_DSN=https://<EMAIL>/project-id
MIXPANEL_TOKEN=your_mixpanel_token_here

# Authentication Configuration
JWT_SECRET=your_jwt_secret_here_minimum_32_characters
SESSION_TIMEOUT=3600000
REMEMBER_ME_DURATION=2592000000

# Development Settings
FLUTTER_ENV=development
DEBUG_MODE=true
LOG_LEVEL=debug

# PWA Configuration
PWA_NAME=Clarity
PWA_SHORT_NAME=Clarity
PWA_DESCRIPTION=Desktop-optimized journaling experience for clarity and mindfulness
PWA_THEME_COLOR=#2196F3
PWA_BACKGROUND_COLOR=#2196F3

# Performance Settings
CACHE_TTL=3600
SERVICE_WORKER_CACHE_VERSION=v1.0.0
OFFLINE_CACHE_SIZE=50

# Feature Flags
ENABLE_OFFLINE_MODE=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=true
ENABLE_REAL_TIME_SYNC=true
ENABLE_VOICE_TO_TEXT=false

# UI Configuration
DEFAULT_THEME=light
ENABLE_DARK_MODE=true
RESPONSIVE_BREAKPOINT_MOBILE=600
RESPONSIVE_BREAKPOINT_TABLET=900
RESPONSIVE_BREAKPOINT_DESKTOP=1200

# Content Configuration
MAX_JOURNAL_ENTRY_LENGTH=10000
AUTO_SAVE_INTERVAL=30000
RICH_TEXT_EDITOR=true

# Localization
DEFAULT_LOCALE=en_US
SUPPORTED_LOCALES=en_US

# Rate Limiting
API_RATE_LIMIT=1000
USER_RATE_LIMIT=100

# Security
ENABLE_CORS=true
CORS_ALLOWED_ORIGINS=http://localhost:8080,https://app.meditatingleo.com
ENABLE_CSP=true
CSP_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'

# File Upload (Optional)
MAX_UPLOAD_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf

# Third-party Integrations (Optional)
GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Email Configuration (Optional)
FROM_EMAIL=<EMAIL>

# WebSocket Configuration (Optional)
WEBSOCKET_URL=wss://your-websocket-url.com
WEBSOCKET_RECONNECT_INTERVAL=5000

# Notifications
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_BROWSER_NOTIFICATIONS=true
NOTIFICATION_CHANNELS=email,browser,in_app

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.1
WEB_VITALS_TRACKING=true

# Build Configuration
BUILD_MODE=debug
TARGET_PLATFORM=web
WEB_RENDERER=canvaskit

# Development Tools
ENABLE_FLUTTER_INSPECTOR=true
ENABLE_PERFORMANCE_OVERLAY=false
ENABLE_DEBUG_BANNER=true

# Testing Configuration (Optional)
TEST_SUPABASE_URL=https://your-test-project.supabase.co
TEST_SUPABASE_ANON_KEY=your_test_supabase_anon_key

# Web Application Branding
APP_LOGO_URL=assets/images/logo.png
COMPANY_NAME=MeditatingLeo
SUPPORT_EMAIL=<EMAIL>
SUPPORT_URL=https://support.meditatingleo.com

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.

# Browser Compatibility
MIN_CHROME_VERSION=90
MIN_FIREFOX_VERSION=88
MIN_SAFARI_VERSION=14
MIN_EDGE_VERSION=90

# SEO Configuration
META_TITLE=ClarityByMeditatingLeo - Mindful Journaling
META_DESCRIPTION=Desktop-optimized journaling experience for clarity and mindfulness
META_KEYWORDS=journaling,mindfulness,clarity,meditation,productivity
META_AUTHOR=MeditatingLeo

# Social Media
SOCIAL_TWITTER=@meditatingleo
SOCIAL_FACEBOOK=meditatingleo
SOCIAL_INSTAGRAM=meditatingleo

# Content Delivery Network (Optional)
CDN_URL=https://cdn.meditatingleo.com
ASSET_BASE_URL=https://assets.meditatingleo.com

# Privacy & Compliance
GDPR_COMPLIANCE=true
COOKIE_CONSENT_REQUIRED=true
DATA_RETENTION_DAYS=365
PRIVACY_POLICY_URL=https://meditatingleo.com/privacy
TERMS_OF_SERVICE_URL=https://meditatingleo.com/terms

# Accessibility
ENABLE_HIGH_CONTRAST=true
ENABLE_SCREEN_READER_SUPPORT=true
ENABLE_KEYBOARD_NAVIGATION=true
FONT_SIZE_SCALING=true

# Backup & Sync
AUTO_BACKUP_ENABLED=true
BACKUP_INTERVAL=3600000
SYNC_CONFLICT_RESOLUTION=client_wins

# Journey & Content
JOURNEY_CACHE_SIZE=20
PROMPT_CACHE_SIZE=50
CONTENT_REFRESH_INTERVAL=300000

# User Experience
ONBOARDING_ENABLED=true
TUTORIAL_ENABLED=true
HELP_SYSTEM_ENABLED=true
FEEDBACK_COLLECTION=true

# Error Handling
ERROR_REPORTING_ENABLED=true
CRASH_REPORTING_ENABLED=true
USER_FEEDBACK_ON_ERRORS=true

# Web App Manifest
MANIFEST_START_URL=/
MANIFEST_DISPLAY=standalone
MANIFEST_ORIENTATION=any
MANIFEST_CATEGORIES=productivity,lifestyle,health
