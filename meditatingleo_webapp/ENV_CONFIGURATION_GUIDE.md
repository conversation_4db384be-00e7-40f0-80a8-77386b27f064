# Environment Configuration Guide

## Overview

The ClarityByMeditatingLeo Web Application now uses `.env` files for configuration management, providing a secure and flexible way to manage environment-specific settings.

## Quick Start

1. **Copy the example file:**
   ```bash
   cp .env.example .env
   ```

2. **Update the required values:**
   ```bash
   # Edit .env file with your actual values
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your_actual_anon_key
   ```

3. **Run the application:**
   ```bash
   flutter run -d web-server --web-port 8080
   ```

## Configuration Files

### `.env.example`
- Template file with all available configuration options
- Safe to commit to version control
- Contains placeholder values and documentation

### `.env`
- Your actual configuration file
- **Never commit this file** (excluded by .gitignore)
- Contains real API keys and sensitive data

### Environment-Specific Files
- `.env.local` - Local development overrides
- `.env.staging` - Staging environment configuration
- `.env.production` - Production environment configuration

## Key Configuration Categories

### 🔐 Required Settings
```env
# Supabase Configuration (Required)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### 🎨 Application Branding
```env
# Web Application Configuration
WEB_APP_NAME=ClarityByMeditatingLeo
WEB_APP_VERSION=1.0.0
PWA_NAME=Clarity
PWA_THEME_COLOR=#2196F3
```

### 🚀 Feature Flags
```env
# Feature Flags
ENABLE_OFFLINE_MODE=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_ANALYTICS=true
ENABLE_REAL_TIME_SYNC=true
```

### 📱 Responsive Design
```env
# UI Configuration
RESPONSIVE_BREAKPOINT_MOBILE=600
RESPONSIVE_BREAKPOINT_TABLET=900
RESPONSIVE_BREAKPOINT_DESKTOP=1200
```

### 🔧 Development Settings
```env
# Development Settings
FLUTTER_ENV=development
DEBUG_MODE=true
LOG_LEVEL=debug
```

## Usage in Code

### Accessing Configuration
```dart
import 'package:meditatingleo_webapp/core/config/env_config.dart';

// Access configuration values
String supabaseUrl = EnvConfig.supabaseUrl;
bool debugMode = EnvConfig.debugMode;
double mobileBreakpoint = EnvConfig.responsiveBreakpointMobile;
```

### Type-Safe Access
The `EnvConfig` class provides type-safe getters with default values:
- `String` values with fallbacks
- `bool` values parsed from strings
- `int` and `double` values with parsing
- `List<String>` values split from comma-separated strings

### Environment Detection
```dart
// Check current environment
if (EnvConfig.isDevelopment) {
  // Development-specific code
}

if (EnvConfig.isProduction) {
  // Production-specific code
}
```

## Security Best Practices

### ✅ Do's
- Use `.env.example` for documentation
- Keep sensitive data in `.env` only
- Use different files for different environments
- Validate required environment variables on startup
- Use meaningful default values

### ❌ Don'ts
- Never commit `.env` files to version control
- Don't hardcode sensitive values in source code
- Don't use production keys in development
- Don't expose API keys in client-side code

## Testing

### Test Configuration
Tests use `dotenv.testLoad()` to provide test-specific values:

```dart
setUpAll(() async {
  dotenv.testLoad(fileInput: '''
SUPABASE_URL=https://test.supabase.co
SUPABASE_ANON_KEY=test-anon-key
WEB_APP_NAME=TestApp
''');
});
```

### Running Tests
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/core/config/env_config_test.dart
```

## Deployment

### Development
```bash
# Use default .env file
flutter run -d web-server
```

### Staging
```bash
# Copy staging configuration
cp .env.staging .env
flutter build web
```

### Production
```bash
# Copy production configuration
cp .env.production .env
flutter build web --release
```

## Troubleshooting

### Common Issues

1. **Environment not initialized error:**
   ```
   Instance of 'NotInitializedError'
   ```
   **Solution:** Ensure `EnvConfig.initialize()` is called in `main()`

2. **Missing .env file:**
   ```
   FileSystemException: Cannot open file
   ```
   **Solution:** Copy `.env.example` to `.env`

3. **Invalid boolean values:**
   ```
   FormatException: Invalid boolean
   ```
   **Solution:** Use `true`/`false` (lowercase) for boolean values

### Debug Mode
Enable debug logging to see configuration loading:
```env
DEBUG_MODE=true
LOG_LEVEL=debug
```

## Migration from Hard-coded Values

### Before (Hard-coded)
```dart
static const String appName = 'ClarityByMeditatingLeo';
static const bool debugMode = true;
```

### After (Environment-based)
```dart
static String get appName => EnvConfig.webAppName;
static bool get debugMode => EnvConfig.debugMode;
```

## Advanced Configuration

### Custom Environment Files
Load specific environment files:
```dart
await dotenv.load(fileName: '.env.staging');
```

### Runtime Configuration
Override values at runtime:
```dart
dotenv.env['CUSTOM_VALUE'] = 'runtime_value';
```

### Validation
Add validation for required values:
```dart
void validateConfig() {
  if (EnvConfig.supabaseUrl.contains('placeholder')) {
    throw Exception('Supabase URL not configured');
  }
}
```

## Support

For configuration issues:
1. Check `.env.example` for all available options
2. Verify file permissions on `.env`
3. Ensure proper syntax (no spaces around `=`)
4. Check the test files for examples
5. Review the `EnvConfig` class for available getters

---

**Last Updated:** January 2025  
**Flutter Version:** 3.24.0+  
**Package:** flutter_dotenv ^5.1.0
