import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:meditatingleo_webapp/shared/providers/app_providers.dart';

// Mock Supabase client for testing
class MockSupabaseClient extends Mock implements SupabaseClient {}

void main() {
  group('App Providers', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: [
          // Override the supabase provider with a mock for testing
          supabaseProvider.overrideWithValue(MockSupabaseClient()),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('supabaseProvider should provide Supabase client', () {
      final supabase = container.read(supabaseProvider);

      expect(supabase, isNotNull);
      expect(supabase, isA<SupabaseClient>());
    });

    test('routerProvider should provide GoRouter instance', () {
      final router = container.read(routerProvider);

      expect(router, isNotNull);
      expect(router.routerDelegate, isNotNull);
    });

    test('themeProvider should provide theme data', () {
      final theme = container.read(themeProvider);

      expect(theme, isNotNull);
      expect(theme.useMaterial3, isTrue);
    });
  });
}
