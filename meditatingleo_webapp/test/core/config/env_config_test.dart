import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'package:meditatingleo_webapp/core/config/env_config.dart';

void main() {
  group('EnvConfig', () {
    setUpAll(() async {
      // Initialize dotenv with test values
      dotenv.testLoad(fileInput: '''
SUPABASE_URL=https://test.supabase.co
SUPABASE_ANON_KEY=test-anon-key
WEB_APP_NAME=TestApp
WEB_APP_VERSION=1.0.0-test
DEBUG_MODE=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=false
PWA_THEME_COLOR=#FF0000
RESPONSIVE_BREAKPOINT_MOBILE=500
CACHE_TTL=7200
''');
    });

    test('should load Supabase configuration from environment', () {
      expect(EnvConfig.supabaseUrl, equals('https://test.supabase.co'));
      expect(EnvConfig.supabaseAnonKey, equals('test-anon-key'));
    });

    test('should load web app configuration from environment', () {
      expect(EnvConfig.webAppName, equals('TestApp'));
      expect(EnvConfig.webAppVersion, equals('1.0.0-test'));
    });

    test('should load boolean values correctly', () {
      expect(EnvConfig.debugMode, isTrue);
      expect(EnvConfig.enableOfflineMode, isTrue);
      expect(EnvConfig.enableAnalytics, isFalse);
    });

    test('should load numeric values correctly', () {
      expect(EnvConfig.responsiveBreakpointMobile, equals(500.0));
      expect(EnvConfig.cacheTtl, equals(7200));
    });

    test('should load PWA configuration', () {
      expect(EnvConfig.pwaName, equals('Clarity')); // Default value
      expect(EnvConfig.pwaShortName, equals('Clarity')); // Default value
    });

    test('should provide default values for missing environment variables', () {
      expect(EnvConfig.pwaShortName, equals('Clarity'));
      expect(EnvConfig.defaultLocale, equals('en_US'));
      expect(EnvConfig.apiRateLimit, equals(1000));
    });

    test('should handle environment detection correctly', () {
      // Test with default development environment
      expect(EnvConfig.isDevelopment, isTrue);
      expect(EnvConfig.isProduction, isFalse);
      expect(EnvConfig.isStaging, isFalse);
    });

    test('should load list values correctly', () {
      expect(EnvConfig.supportedLocales, contains('en_US'));
      expect(EnvConfig.corsAllowedOrigins, contains('http://localhost:8080'));
      expect(EnvConfig.manifestCategories,
          containsAll(['productivity', 'lifestyle', 'health']));
    });

    test('should handle optional values correctly', () {
      expect(EnvConfig.sentryDsn, isNull);
      expect(EnvConfig.mixpanelToken, isNull);
      expect(EnvConfig.googleAnalyticsId, isNull);
    });
  });
}
