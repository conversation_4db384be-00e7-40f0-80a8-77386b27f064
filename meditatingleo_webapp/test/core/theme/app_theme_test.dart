import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:meditatingleo_webapp/core/theme/app_theme.dart';

void main() {
  group('AppTheme', () {
    test('should create light theme with Material Design 3', () {
      final theme = AppTheme.lightTheme;

      expect(theme.useMaterial3, isTrue);
      expect(theme.brightness, equals(Brightness.light));
    });

    test('should create dark theme with Material Design 3', () {
      final theme = AppTheme.darkTheme;

      expect(theme.useMaterial3, isTrue);
      expect(theme.brightness, equals(Brightness.dark));
    });

    test('should use modern color API', () {
      final theme = AppTheme.lightTheme;
      
      // Test that we're using modern color values
      expect(theme.colorScheme, isNotNull);
      expect(theme.colorScheme.primary, isNotNull);
    });

    test('should have web-optimized typography', () {
      final theme = AppTheme.lightTheme;
      
      expect(theme.textTheme, isNotNull);
      expect(theme.textTheme.headlineLarge, isNotNull);
    });
  });
}
