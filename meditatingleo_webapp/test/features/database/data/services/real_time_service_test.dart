import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_webapp/features/database/data/services/real_time_service.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

import 'real_time_service_test.mocks.dart';

@GenerateMocks([SupabaseClient, RealtimeChannel])
void main() {
  group('RealTimeService', () {
    late RealTimeService service;
    late MockSupabaseClient mockSupabaseClient;
    late MockRealtimeChannel mockChannel;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockChannel = MockRealtimeChannel();
      service = RealTimeService(mockSupabaseClient);
    });

    group('subscribeToTable', () {
      test('should subscribe to table changes successfully', () async {
        // Arrange
        const tableName = 'journal_entries';
        when(mockSupabaseClient.channel('public:$tableName'))
            .thenReturn(mockChannel);
        when(mockChannel.onPostgresChanges(
          event: anyNamed('event'),
          schema: anyNamed('schema'),
          table: anyNamed('table'),
          callback: anyNamed('callback'),
        )).thenReturn(mockChannel);
        when(mockChannel.subscribe()).thenAnswer((_) async => RealtimeSubscribeStatus.subscribed);

        // Act
        final result = await service.subscribeToTable(
          tableName,
          (payload) => print('Change detected: $payload'),
        );

        // Assert
        expect(result.isSuccess, true);
        verify(mockSupabaseClient.channel('public:$tableName')).called(1);
        verify(mockChannel.onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: tableName,
          callback: anyNamed('callback'),
        )).called(1);
        verify(mockChannel.subscribe()).called(1);
      });

      test('should return error when subscription fails', () async {
        // Arrange
        const tableName = 'journal_entries';
        when(mockSupabaseClient.channel('public:$tableName'))
            .thenReturn(mockChannel);
        when(mockChannel.onPostgresChanges(
          event: anyNamed('event'),
          schema: anyNamed('schema'),
          table: anyNamed('table'),
          callback: anyNamed('callback'),
        )).thenReturn(mockChannel);
        when(mockChannel.subscribe()).thenAnswer((_) async => RealtimeSubscribeStatus.timedOut);

        // Act
        final result = await service.subscribeToTable(
          tableName,
          (payload) => print('Change detected: $payload'),
        );

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Failed to subscribe'));
      });
    });

    group('subscribeToUserData', () {
      test('should subscribe to user-specific data successfully', () async {
        // Arrange
        const userId = 'user-123';
        const tableName = 'journal_entries';
        when(mockSupabaseClient.channel('user:$userId:$tableName'))
            .thenReturn(mockChannel);
        when(mockChannel.onPostgresChanges(
          event: anyNamed('event'),
          schema: anyNamed('schema'),
          table: anyNamed('table'),
          filter: anyNamed('filter'),
          callback: anyNamed('callback'),
        )).thenReturn(mockChannel);
        when(mockChannel.subscribe()).thenAnswer((_) async => RealtimeSubscribeStatus.subscribed);

        // Act
        final result = await service.subscribeToUserData(
          userId,
          tableName,
          (payload) => print('User data changed: $payload'),
        );

        // Assert
        expect(result.isSuccess, true);
        verify(mockSupabaseClient.channel('user:$userId:$tableName')).called(1);
        verify(mockChannel.onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: tableName,
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: anyNamed('callback'),
        )).called(1);
        verify(mockChannel.subscribe()).called(1);
      });

      test('should return error when user data subscription fails', () async {
        // Arrange
        const userId = 'user-123';
        const tableName = 'journal_entries';
        when(mockSupabaseClient.channel('user:$userId:$tableName'))
            .thenReturn(mockChannel);
        when(mockChannel.onPostgresChanges(
          event: anyNamed('event'),
          schema: anyNamed('schema'),
          table: anyNamed('table'),
          filter: anyNamed('filter'),
          callback: anyNamed('callback'),
        )).thenReturn(mockChannel);
        when(mockChannel.subscribe()).thenAnswer((_) async => RealtimeSubscribeStatus.channelError);

        // Act
        final result = await service.subscribeToUserData(
          userId,
          tableName,
          (payload) => print('User data changed: $payload'),
        );

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Failed to subscribe'));
      });
    });

    group('unsubscribe', () {
      test('should unsubscribe from channel successfully', () async {
        // Arrange
        const channelName = 'test-channel';
        when(mockSupabaseClient.removeChannel(mockChannel))
            .thenAnswer((_) async => RealtimeSubscribeStatus.closed);

        // Act
        final result = await service.unsubscribe(channelName);

        // Assert
        expect(result.isSuccess, true);
      });

      test('should return error when unsubscribe fails', () async {
        // Arrange
        const channelName = 'test-channel';
        when(mockSupabaseClient.removeChannel(any))
            .thenThrow(Exception('Unsubscribe failed'));

        // Act
        final result = await service.unsubscribe(channelName);

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Failed to unsubscribe'));
      });
    });

    group('unsubscribeAll', () {
      test('should unsubscribe from all channels successfully', () async {
        // Arrange
        when(mockSupabaseClient.removeAllChannels())
            .thenAnswer((_) async => [RealtimeSubscribeStatus.closed]);

        // Act
        final result = await service.unsubscribeAll();

        // Assert
        expect(result.isSuccess, true);
        verify(mockSupabaseClient.removeAllChannels()).called(1);
      });

      test('should return error when unsubscribe all fails', () async {
        // Arrange
        when(mockSupabaseClient.removeAllChannels())
            .thenThrow(Exception('Unsubscribe all failed'));

        // Act
        final result = await service.unsubscribeAll();

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Failed to unsubscribe'));
      });
    });

    group('isConnected', () {
      test('should return connection status', () {
        // Arrange
        when(mockSupabaseClient.realtime.isConnected).thenReturn(true);

        // Act
        final isConnected = service.isConnected;

        // Assert
        expect(isConnected, true);
        verify(mockSupabaseClient.realtime.isConnected).called(1);
      });
    });

    group('connectionStream', () {
      test('should provide connection status stream', () {
        // Arrange
        final connectionStream = Stream.fromIterable([
          RealtimeSubscribeStatus.subscribed,
          RealtimeSubscribeStatus.closed,
        ]);
        when(mockSupabaseClient.realtime.onOpen).thenAnswer((_) => connectionStream);

        // Act
        final stream = service.connectionStream;

        // Assert
        expect(stream, isA<Stream<bool>>());
      });
    });
  });
}
