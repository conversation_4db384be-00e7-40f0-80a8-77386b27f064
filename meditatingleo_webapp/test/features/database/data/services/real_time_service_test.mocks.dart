// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_webapp/test/features/database/data/services/real_time_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;
import 'package:realtime_client/src/constants.dart' as _i7;
import 'package:realtime_client/src/push.dart' as _i3;
import 'package:realtime_client/src/types.dart' as _i6;
import 'package:supabase/supabase.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFunctionsClient_0 extends _i1.SmartFake
    implements _i2.FunctionsClient {
  _FakeFunctionsClient_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseStorageClient_1 extends _i1.SmartFake
    implements _i2.SupabaseStorageClient {
  _FakeSupabaseStorageClient_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRealtimeClient_2 extends _i1.SmartFake
    implements _i2.RealtimeClient {
  _FakeRealtimeClient_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestClient_3 extends _i1.SmartFake
    implements _i2.PostgrestClient {
  _FakePostgrestClient_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGoTrueClient_4 extends _i1.SmartFake implements _i2.GoTrueClient {
  _FakeGoTrueClient_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseQueryBuilder_5 extends _i1.SmartFake
    implements _i2.SupabaseQueryBuilder {
  _FakeSupabaseQueryBuilder_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseQuerySchema_6 extends _i1.SmartFake
    implements _i2.SupabaseQuerySchema {
  _FakeSupabaseQuerySchema_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestFilterBuilder_7<T1> extends _i1.SmartFake
    implements _i2.PostgrestFilterBuilder<T1> {
  _FakePostgrestFilterBuilder_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRealtimeChannel_8 extends _i1.SmartFake
    implements _i2.RealtimeChannel {
  _FakeRealtimeChannel_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePush_9 extends _i1.SmartFake implements _i3.Push {
  _FakePush_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRealtimePresence_10 extends _i1.SmartFake
    implements _i2.RealtimePresence {
  _FakeRealtimePresence_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SupabaseClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseClient extends _i1.Mock implements _i2.SupabaseClient {
  MockSupabaseClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FunctionsClient get functions => (super.noSuchMethod(
        Invocation.getter(#functions),
        returnValue: _FakeFunctionsClient_0(
          this,
          Invocation.getter(#functions),
        ),
      ) as _i2.FunctionsClient);

  @override
  _i2.SupabaseStorageClient get storage => (super.noSuchMethod(
        Invocation.getter(#storage),
        returnValue: _FakeSupabaseStorageClient_1(
          this,
          Invocation.getter(#storage),
        ),
      ) as _i2.SupabaseStorageClient);

  @override
  _i2.RealtimeClient get realtime => (super.noSuchMethod(
        Invocation.getter(#realtime),
        returnValue: _FakeRealtimeClient_2(
          this,
          Invocation.getter(#realtime),
        ),
      ) as _i2.RealtimeClient);

  @override
  _i2.PostgrestClient get rest => (super.noSuchMethod(
        Invocation.getter(#rest),
        returnValue: _FakePostgrestClient_3(
          this,
          Invocation.getter(#rest),
        ),
      ) as _i2.PostgrestClient);

  @override
  Map<String, String> get headers => (super.noSuchMethod(
        Invocation.getter(#headers),
        returnValue: <String, String>{},
      ) as Map<String, String>);

  @override
  _i2.GoTrueClient get auth => (super.noSuchMethod(
        Invocation.getter(#auth),
        returnValue: _FakeGoTrueClient_4(
          this,
          Invocation.getter(#auth),
        ),
      ) as _i2.GoTrueClient);

  @override
  set functions(_i2.FunctionsClient? _functions) => super.noSuchMethod(
        Invocation.setter(
          #functions,
          _functions,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set storage(_i2.SupabaseStorageClient? _storage) => super.noSuchMethod(
        Invocation.setter(
          #storage,
          _storage,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set realtime(_i2.RealtimeClient? _realtime) => super.noSuchMethod(
        Invocation.setter(
          #realtime,
          _realtime,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set rest(_i2.PostgrestClient? _rest) => super.noSuchMethod(
        Invocation.setter(
          #rest,
          _rest,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set headers(Map<String, String>? headers) => super.noSuchMethod(
        Invocation.setter(
          #headers,
          headers,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i2.SupabaseQueryBuilder from(String? table) => (super.noSuchMethod(
        Invocation.method(
          #from,
          [table],
        ),
        returnValue: _FakeSupabaseQueryBuilder_5(
          this,
          Invocation.method(
            #from,
            [table],
          ),
        ),
      ) as _i2.SupabaseQueryBuilder);

  @override
  _i2.SupabaseQuerySchema schema(String? schema) => (super.noSuchMethod(
        Invocation.method(
          #schema,
          [schema],
        ),
        returnValue: _FakeSupabaseQuerySchema_6(
          this,
          Invocation.method(
            #schema,
            [schema],
          ),
        ),
      ) as _i2.SupabaseQuerySchema);

  @override
  _i2.PostgrestFilterBuilder<T> rpc<T>(
    String? fn, {
    Map<String, dynamic>? params,
    dynamic get = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #rpc,
          [fn],
          {
            #params: params,
            #get: get,
          },
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #rpc,
            [fn],
            {
              #params: params,
              #get: get,
            },
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.RealtimeChannel channel(
    String? name, {
    _i2.RealtimeChannelConfig? opts = const _i2.RealtimeChannelConfig(),
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #channel,
          [name],
          {#opts: opts},
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #channel,
            [name],
            {#opts: opts},
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  List<_i2.RealtimeChannel> getChannels() => (super.noSuchMethod(
        Invocation.method(
          #getChannels,
          [],
        ),
        returnValue: <_i2.RealtimeChannel>[],
      ) as List<_i2.RealtimeChannel>);

  @override
  _i4.Future<String> removeChannel(_i2.RealtimeChannel? channel) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeChannel,
          [channel],
        ),
        returnValue: _i4.Future<String>.value(_i5.dummyValue<String>(
          this,
          Invocation.method(
            #removeChannel,
            [channel],
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<List<String>> removeAllChannels() => (super.noSuchMethod(
        Invocation.method(
          #removeAllChannels,
          [],
        ),
        returnValue: _i4.Future<List<String>>.value(<String>[]),
      ) as _i4.Future<List<String>>);

  @override
  _i4.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}

/// A class which mocks [RealtimeChannel].
///
/// See the documentation for Mockito's code generation for more information.
class MockRealtimeChannel extends _i1.Mock implements _i2.RealtimeChannel {
  MockRealtimeChannel() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get joinedOnce => (super.noSuchMethod(
        Invocation.getter(#joinedOnce),
        returnValue: false,
      ) as bool);

  @override
  _i3.Push get joinPush => (super.noSuchMethod(
        Invocation.getter(#joinPush),
        returnValue: _FakePush_9(
          this,
          Invocation.getter(#joinPush),
        ),
      ) as _i3.Push);

  @override
  _i2.RealtimePresence get presence => (super.noSuchMethod(
        Invocation.getter(#presence),
        returnValue: _FakeRealtimePresence_10(
          this,
          Invocation.getter(#presence),
        ),
      ) as _i2.RealtimePresence);

  @override
  String get broadcastEndpointURL => (super.noSuchMethod(
        Invocation.getter(#broadcastEndpointURL),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#broadcastEndpointURL),
        ),
      ) as String);

  @override
  String get subTopic => (super.noSuchMethod(
        Invocation.getter(#subTopic),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#subTopic),
        ),
      ) as String);

  @override
  String get topic => (super.noSuchMethod(
        Invocation.getter(#topic),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#topic),
        ),
      ) as String);

  @override
  Map<String, dynamic> get params => (super.noSuchMethod(
        Invocation.getter(#params),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i2.RealtimeClient get socket => (super.noSuchMethod(
        Invocation.getter(#socket),
        returnValue: _FakeRealtimeClient_2(
          this,
          Invocation.getter(#socket),
        ),
      ) as _i2.RealtimeClient);

  @override
  bool get canPush => (super.noSuchMethod(
        Invocation.getter(#canPush),
        returnValue: false,
      ) as bool);

  @override
  String get joinRef => (super.noSuchMethod(
        Invocation.getter(#joinRef),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#joinRef),
        ),
      ) as String);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
      ) as bool);

  @override
  bool get isErrored => (super.noSuchMethod(
        Invocation.getter(#isErrored),
        returnValue: false,
      ) as bool);

  @override
  bool get isJoined => (super.noSuchMethod(
        Invocation.getter(#isJoined),
        returnValue: false,
      ) as bool);

  @override
  bool get isJoining => (super.noSuchMethod(
        Invocation.getter(#isJoining),
        returnValue: false,
      ) as bool);

  @override
  bool get isLeaving => (super.noSuchMethod(
        Invocation.getter(#isLeaving),
        returnValue: false,
      ) as bool);

  @override
  set joinedOnce(bool? _joinedOnce) => super.noSuchMethod(
        Invocation.setter(
          #joinedOnce,
          _joinedOnce,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set joinPush(_i3.Push? _joinPush) => super.noSuchMethod(
        Invocation.setter(
          #joinPush,
          _joinPush,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set presence(_i2.RealtimePresence? _presence) => super.noSuchMethod(
        Invocation.setter(
          #presence,
          _presence,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set broadcastEndpointURL(String? _broadcastEndpointURL) => super.noSuchMethod(
        Invocation.setter(
          #broadcastEndpointURL,
          _broadcastEndpointURL,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set params(Map<String, dynamic>? _params) => super.noSuchMethod(
        Invocation.setter(
          #params,
          _params,
        ),
        returnValueForMissingStub: null,
      );

  @override
  void rejoinUntilConnected() => super.noSuchMethod(
        Invocation.method(
          #rejoinUntilConnected,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i2.RealtimeChannel subscribe([
    void Function(
      _i2.RealtimeSubscribeStatus,
      Object?,
    )? callback,
    Duration? timeout,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribe,
          [
            callback,
            timeout,
          ],
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #subscribe,
            [
              callback,
              timeout,
            ],
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  List<_i2.SinglePresenceState> presenceState() => (super.noSuchMethod(
        Invocation.method(
          #presenceState,
          [],
        ),
        returnValue: <_i2.SinglePresenceState>[],
      ) as List<_i2.SinglePresenceState>);

  @override
  _i4.Future<_i2.ChannelResponse> track(
    Map<String, dynamic>? payload, [
    Map<String, dynamic>? opts = const {},
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #track,
          [
            payload,
            opts,
          ],
        ),
        returnValue:
            _i4.Future<_i2.ChannelResponse>.value(_i2.ChannelResponse.ok),
      ) as _i4.Future<_i2.ChannelResponse>);

  @override
  _i4.Future<_i2.ChannelResponse> untrack(
          [Map<String, dynamic>? opts = const {}]) =>
      (super.noSuchMethod(
        Invocation.method(
          #untrack,
          [opts],
        ),
        returnValue:
            _i4.Future<_i2.ChannelResponse>.value(_i2.ChannelResponse.ok),
      ) as _i4.Future<_i2.ChannelResponse>);

  @override
  _i2.RealtimeChannel onPostgresChanges({
    required _i2.PostgresChangeEvent? event,
    String? schema,
    String? table,
    _i2.PostgresChangeFilter? filter,
    required void Function(_i2.PostgresChangePayload)? callback,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #onPostgresChanges,
          [],
          {
            #event: event,
            #schema: schema,
            #table: table,
            #filter: filter,
            #callback: callback,
          },
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #onPostgresChanges,
            [],
            {
              #event: event,
              #schema: schema,
              #table: table,
              #filter: filter,
              #callback: callback,
            },
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  _i2.RealtimeChannel onBroadcast({
    required String? event,
    required void Function(Map<String, dynamic>)? callback,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #onBroadcast,
          [],
          {
            #event: event,
            #callback: callback,
          },
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #onBroadcast,
            [],
            {
              #event: event,
              #callback: callback,
            },
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  _i2.RealtimeChannel onPresenceSync(
          void Function(_i2.RealtimePresenceSyncPayload)? callback) =>
      (super.noSuchMethod(
        Invocation.method(
          #onPresenceSync,
          [callback],
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #onPresenceSync,
            [callback],
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  _i2.RealtimeChannel onPresenceJoin(
          void Function(_i2.RealtimePresenceJoinPayload)? callback) =>
      (super.noSuchMethod(
        Invocation.method(
          #onPresenceJoin,
          [callback],
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #onPresenceJoin,
            [callback],
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  _i2.RealtimeChannel onPresenceLeave(
          void Function(_i2.RealtimePresenceLeavePayload)? callback) =>
      (super.noSuchMethod(
        Invocation.method(
          #onPresenceLeave,
          [callback],
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #onPresenceLeave,
            [callback],
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  _i2.RealtimeChannel onSystemEvents(void Function(dynamic)? callback) =>
      (super.noSuchMethod(
        Invocation.method(
          #onSystemEvents,
          [callback],
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #onSystemEvents,
            [callback],
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  _i2.RealtimeChannel onEvents(
    String? type,
    _i6.ChannelFilter? filter,
    _i2.BindingCallback? callback,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #onEvents,
          [
            type,
            filter,
            callback,
          ],
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #onEvents,
            [
              type,
              filter,
              callback,
            ],
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  _i2.RealtimeChannel off(
    String? type,
    Map<String, String>? filter,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #off,
          [
            type,
            filter,
          ],
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #off,
            [
              type,
              filter,
            ],
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  _i3.Push push(
    _i7.ChannelEvents? event,
    Map<String, dynamic>? payload, [
    Duration? timeout,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #push,
          [
            event,
            payload,
            timeout,
          ],
        ),
        returnValue: _FakePush_9(
          this,
          Invocation.method(
            #push,
            [
              event,
              payload,
              timeout,
            ],
          ),
        ),
      ) as _i3.Push);

  @override
  _i4.Future<_i2.ChannelResponse> sendBroadcastMessage({
    required String? event,
    required Map<String, dynamic>? payload,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendBroadcastMessage,
          [],
          {
            #event: event,
            #payload: payload,
          },
        ),
        returnValue:
            _i4.Future<_i2.ChannelResponse>.value(_i2.ChannelResponse.ok),
      ) as _i4.Future<_i2.ChannelResponse>);

  @override
  _i4.Future<_i2.ChannelResponse> send({
    required _i6.RealtimeListenTypes? type,
    String? event,
    required Map<String, dynamic>? payload,
    Map<String, dynamic>? opts = const {},
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #send,
          [],
          {
            #type: type,
            #event: event,
            #payload: payload,
            #opts: opts,
          },
        ),
        returnValue:
            _i4.Future<_i2.ChannelResponse>.value(_i2.ChannelResponse.ok),
      ) as _i4.Future<_i2.ChannelResponse>);

  @override
  void updateJoinPayload(Map<String, dynamic>? payload) => super.noSuchMethod(
        Invocation.method(
          #updateJoinPayload,
          [payload],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<String> unsubscribe([Duration? timeout]) => (super.noSuchMethod(
        Invocation.method(
          #unsubscribe,
          [timeout],
        ),
        returnValue: _i4.Future<String>.value(_i5.dummyValue<String>(
          this,
          Invocation.method(
            #unsubscribe,
            [timeout],
          ),
        )),
      ) as _i4.Future<String>);

  @override
  dynamic onMessage(
    String? event,
    dynamic payload, [
    String? ref,
  ]) =>
      super.noSuchMethod(Invocation.method(
        #onMessage,
        [
          event,
          payload,
          ref,
        ],
      ));

  @override
  bool isMember(String? topic) => (super.noSuchMethod(
        Invocation.method(
          #isMember,
          [topic],
        ),
        returnValue: false,
      ) as bool);

  @override
  void rejoin([Duration? timeout]) => super.noSuchMethod(
        Invocation.method(
          #rejoin,
          [timeout],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void forceRejoin([Duration? timeout]) => super.noSuchMethod(
        Invocation.method(
          #forceRejoin,
          [timeout],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void trigger(
    String? type, [
    dynamic payload,
    String? ref,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #trigger,
          [
            type,
            payload,
            ref,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  String replyEventName(String? ref) => (super.noSuchMethod(
        Invocation.method(
          #replyEventName,
          [ref],
        ),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.method(
            #replyEventName,
            [ref],
          ),
        ),
      ) as String);
}
