import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_webapp/features/database/data/repositories/web_database_repository.dart';
import 'package:meditatingleo_webapp/features/database/data/services/web_database_service.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

import 'web_database_repository_test.mocks.dart';

@GenerateMocks([WebDatabaseService, SupabaseClient])
void main() {
  group('WebDatabaseRepository', () {
    late WebDatabaseRepository repository;
    late MockWebDatabaseService mockDatabaseService;
    late MockSupabaseClient mockSupabaseClient;

    setUp(() {
      mockDatabaseService = MockWebDatabaseService();
      mockSupabaseClient = MockSupabaseClient();
      repository = WebDatabaseRepository(mockDatabaseService);
    });

    group('initialize', () {
      test('should initialize database successfully', () async {
        // Arrange
        when(mockDatabaseService.initialize())
            .thenAnswer((_) async => const Result.success(true));

        // Act
        final result = await repository.initialize();

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, true);
        verify(mockDatabaseService.initialize()).called(1);
      });

      test('should return error when initialization fails', () async {
        // Arrange
        when(mockDatabaseService.initialize()).thenAnswer((_) async =>
            const Result.failure(AppError.database('Initialization failed')));

        // Act
        final result = await repository.initialize();

        // Assert
        expect(result.isFailure, true);
        expect(
            result.error?.technicalMessage, contains('Initialization failed'));
        verify(mockDatabaseService.initialize()).called(1);
      });
    });

    group('query', () {
      test('should execute query successfully', () async {
        // Arrange
        const table = 'test_table';
        final mockData = [
          {'id': 1, 'name': 'test'}
        ];
        when(mockDatabaseService.query(table))
            .thenAnswer((_) async => Result.success(mockData));

        // Act
        final result = await repository.query(table);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, mockData);
        verify(mockDatabaseService.query(table)).called(1);
      });

      test('should return error when query fails', () async {
        // Arrange
        const table = 'test_table';
        when(mockDatabaseService.query(table)).thenAnswer((_) async =>
            const Result.failure(AppError.database('Query failed')));

        // Act
        final result = await repository.query(table);

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Query failed'));
        verify(mockDatabaseService.query(table)).called(1);
      });
    });

    group('insert', () {
      test('should insert data successfully', () async {
        // Arrange
        const table = 'test_table';
        final data = {'name': 'test'};
        final insertedData = {'id': 1, 'name': 'test'};
        when(mockDatabaseService.insert(table, data))
            .thenAnswer((_) async => Result.success(insertedData));

        // Act
        final result = await repository.insert(table, data);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, insertedData);
        verify(mockDatabaseService.insert(table, data)).called(1);
      });

      test('should return error when insert fails', () async {
        // Arrange
        const table = 'test_table';
        final data = {'name': 'test'};
        when(mockDatabaseService.insert(table, data)).thenAnswer((_) async =>
            const Result.failure(AppError.database('Insert failed')));

        // Act
        final result = await repository.insert(table, data);

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Insert failed'));
        verify(mockDatabaseService.insert(table, data)).called(1);
      });
    });

    group('update', () {
      test('should update data successfully', () async {
        // Arrange
        const table = 'test_table';
        const id = 1;
        final data = {'name': 'updated'};
        final updatedData = {'id': 1, 'name': 'updated'};
        when(mockDatabaseService.update(table, id, data))
            .thenAnswer((_) async => Result.success(updatedData));

        // Act
        final result = await repository.update(table, id, data);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, updatedData);
        verify(mockDatabaseService.update(table, id, data)).called(1);
      });

      test('should return error when update fails', () async {
        // Arrange
        const table = 'test_table';
        const id = 1;
        final data = {'name': 'updated'};
        when(mockDatabaseService.update(table, id, data)).thenAnswer(
            (_) async =>
                const Result.failure(AppError.database('Update failed')));

        // Act
        final result = await repository.update(table, id, data);

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Update failed'));
        verify(mockDatabaseService.update(table, id, data)).called(1);
      });
    });

    group('delete', () {
      test('should delete data successfully', () async {
        // Arrange
        const table = 'test_table';
        const id = 1;
        when(mockDatabaseService.delete(table, id))
            .thenAnswer((_) async => const Result.success(true));

        // Act
        final result = await repository.delete(table, id);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, true);
        verify(mockDatabaseService.delete(table, id)).called(1);
      });

      test('should return error when delete fails', () async {
        // Arrange
        const table = 'test_table';
        const id = 1;
        when(mockDatabaseService.delete(table, id)).thenAnswer((_) async =>
            const Result.failure(AppError.database('Delete failed')));

        // Act
        final result = await repository.delete(table, id);

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Delete failed'));
        verify(mockDatabaseService.delete(table, id)).called(1);
      });
    });
  });
}
