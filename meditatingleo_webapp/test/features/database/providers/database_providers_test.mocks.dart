// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_webapp/test/features/database/providers/database_providers_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:meditatingleo_webapp/features/database/data/repositories/web_database_repository.dart'
    as _i10;
import 'package:meditatingleo_webapp/features/database/data/services/cache_service.dart'
    as _i9;
import 'package:meditatingleo_webapp/features/database/data/services/real_time_service.dart'
    as _i8;
import 'package:meditatingleo_webapp/features/database/data/services/web_database_service.dart'
    as _i6;
import 'package:meditatingleo_webapp/shared/models/app_error.dart' as _i7;
import 'package:meditatingleo_webapp/shared/models/result.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;
import 'package:supabase/supabase.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFunctionsClient_0 extends _i1.SmartFake
    implements _i2.FunctionsClient {
  _FakeFunctionsClient_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseStorageClient_1 extends _i1.SmartFake
    implements _i2.SupabaseStorageClient {
  _FakeSupabaseStorageClient_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRealtimeClient_2 extends _i1.SmartFake
    implements _i2.RealtimeClient {
  _FakeRealtimeClient_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestClient_3 extends _i1.SmartFake
    implements _i2.PostgrestClient {
  _FakePostgrestClient_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGoTrueClient_4 extends _i1.SmartFake implements _i2.GoTrueClient {
  _FakeGoTrueClient_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseQueryBuilder_5 extends _i1.SmartFake
    implements _i2.SupabaseQueryBuilder {
  _FakeSupabaseQueryBuilder_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseQuerySchema_6 extends _i1.SmartFake
    implements _i2.SupabaseQuerySchema {
  _FakeSupabaseQuerySchema_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePostgrestFilterBuilder_7<T1> extends _i1.SmartFake
    implements _i2.PostgrestFilterBuilder<T1> {
  _FakePostgrestFilterBuilder_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRealtimeChannel_8 extends _i1.SmartFake
    implements _i2.RealtimeChannel {
  _FakeRealtimeChannel_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSupabaseClient_9 extends _i1.SmartFake
    implements _i2.SupabaseClient {
  _FakeSupabaseClient_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeResult_10<T, E> extends _i1.SmartFake implements _i3.Result<T, E> {
  _FakeResult_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SupabaseClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseClient extends _i1.Mock implements _i2.SupabaseClient {
  MockSupabaseClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FunctionsClient get functions => (super.noSuchMethod(
        Invocation.getter(#functions),
        returnValue: _FakeFunctionsClient_0(
          this,
          Invocation.getter(#functions),
        ),
      ) as _i2.FunctionsClient);

  @override
  _i2.SupabaseStorageClient get storage => (super.noSuchMethod(
        Invocation.getter(#storage),
        returnValue: _FakeSupabaseStorageClient_1(
          this,
          Invocation.getter(#storage),
        ),
      ) as _i2.SupabaseStorageClient);

  @override
  _i2.RealtimeClient get realtime => (super.noSuchMethod(
        Invocation.getter(#realtime),
        returnValue: _FakeRealtimeClient_2(
          this,
          Invocation.getter(#realtime),
        ),
      ) as _i2.RealtimeClient);

  @override
  _i2.PostgrestClient get rest => (super.noSuchMethod(
        Invocation.getter(#rest),
        returnValue: _FakePostgrestClient_3(
          this,
          Invocation.getter(#rest),
        ),
      ) as _i2.PostgrestClient);

  @override
  Map<String, String> get headers => (super.noSuchMethod(
        Invocation.getter(#headers),
        returnValue: <String, String>{},
      ) as Map<String, String>);

  @override
  _i2.GoTrueClient get auth => (super.noSuchMethod(
        Invocation.getter(#auth),
        returnValue: _FakeGoTrueClient_4(
          this,
          Invocation.getter(#auth),
        ),
      ) as _i2.GoTrueClient);

  @override
  set functions(_i2.FunctionsClient? _functions) => super.noSuchMethod(
        Invocation.setter(
          #functions,
          _functions,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set storage(_i2.SupabaseStorageClient? _storage) => super.noSuchMethod(
        Invocation.setter(
          #storage,
          _storage,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set realtime(_i2.RealtimeClient? _realtime) => super.noSuchMethod(
        Invocation.setter(
          #realtime,
          _realtime,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set rest(_i2.PostgrestClient? _rest) => super.noSuchMethod(
        Invocation.setter(
          #rest,
          _rest,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set headers(Map<String, String>? headers) => super.noSuchMethod(
        Invocation.setter(
          #headers,
          headers,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i2.SupabaseQueryBuilder from(String? table) => (super.noSuchMethod(
        Invocation.method(
          #from,
          [table],
        ),
        returnValue: _FakeSupabaseQueryBuilder_5(
          this,
          Invocation.method(
            #from,
            [table],
          ),
        ),
      ) as _i2.SupabaseQueryBuilder);

  @override
  _i2.SupabaseQuerySchema schema(String? schema) => (super.noSuchMethod(
        Invocation.method(
          #schema,
          [schema],
        ),
        returnValue: _FakeSupabaseQuerySchema_6(
          this,
          Invocation.method(
            #schema,
            [schema],
          ),
        ),
      ) as _i2.SupabaseQuerySchema);

  @override
  _i2.PostgrestFilterBuilder<T> rpc<T>(
    String? fn, {
    Map<String, dynamic>? params,
    dynamic get = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #rpc,
          [fn],
          {
            #params: params,
            #get: get,
          },
        ),
        returnValue: _FakePostgrestFilterBuilder_7<T>(
          this,
          Invocation.method(
            #rpc,
            [fn],
            {
              #params: params,
              #get: get,
            },
          ),
        ),
      ) as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.RealtimeChannel channel(
    String? name, {
    _i2.RealtimeChannelConfig? opts = const _i2.RealtimeChannelConfig(),
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #channel,
          [name],
          {#opts: opts},
        ),
        returnValue: _FakeRealtimeChannel_8(
          this,
          Invocation.method(
            #channel,
            [name],
            {#opts: opts},
          ),
        ),
      ) as _i2.RealtimeChannel);

  @override
  List<_i2.RealtimeChannel> getChannels() => (super.noSuchMethod(
        Invocation.method(
          #getChannels,
          [],
        ),
        returnValue: <_i2.RealtimeChannel>[],
      ) as List<_i2.RealtimeChannel>);

  @override
  _i4.Future<String> removeChannel(_i2.RealtimeChannel? channel) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeChannel,
          [channel],
        ),
        returnValue: _i4.Future<String>.value(_i5.dummyValue<String>(
          this,
          Invocation.method(
            #removeChannel,
            [channel],
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<List<String>> removeAllChannels() => (super.noSuchMethod(
        Invocation.method(
          #removeAllChannels,
          [],
        ),
        returnValue: _i4.Future<List<String>>.value(<String>[]),
      ) as _i4.Future<List<String>>);

  @override
  _i4.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}

/// A class which mocks [WebDatabaseService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebDatabaseService extends _i1.Mock
    implements _i6.WebDatabaseService {
  MockWebDatabaseService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.SupabaseClient get client => (super.noSuchMethod(
        Invocation.getter(#client),
        returnValue: _FakeSupabaseClient_9(
          this,
          Invocation.getter(#client),
        ),
      ) as _i2.SupabaseClient);

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> initialize() =>
      (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #initialize,
            [],
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>> query(
          String? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #query,
          [table],
        ),
        returnValue: _i4
            .Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>.value(
            _FakeResult_10<List<Map<String, dynamic>>, _i7.AppError>(
          this,
          Invocation.method(
            #query,
            [table],
          ),
        )),
      ) as _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>> insert(
    String? table,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #insert,
          [
            table,
            data,
          ],
        ),
        returnValue:
            _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>.value(
                _FakeResult_10<Map<String, dynamic>, _i7.AppError>(
          this,
          Invocation.method(
            #insert,
            [
              table,
              data,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>> update(
    String? table,
    dynamic id,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [
            table,
            id,
            data,
          ],
        ),
        returnValue:
            _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>.value(
                _FakeResult_10<Map<String, dynamic>, _i7.AppError>(
          this,
          Invocation.method(
            #update,
            [
              table,
              id,
              data,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> delete(
    String? table,
    dynamic id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [
            table,
            id,
          ],
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #delete,
            [
              table,
              id,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<
      _i3.Result<List<Map<String, dynamic>>, _i7.AppError>> queryWithFilters(
    String? table, {
    Map<String, dynamic>? filters,
    String? orderBy,
    bool? ascending = true,
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #queryWithFilters,
          [table],
          {
            #filters: filters,
            #orderBy: orderBy,
            #ascending: ascending,
            #limit: limit,
          },
        ),
        returnValue: _i4
            .Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>.value(
            _FakeResult_10<List<Map<String, dynamic>>, _i7.AppError>(
          this,
          Invocation.method(
            #queryWithFilters,
            [table],
            {
              #filters: filters,
              #orderBy: orderBy,
              #ascending: ascending,
              #limit: limit,
            },
          ),
        )),
      ) as _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>> batchInsert(
    String? table,
    List<Map<String, dynamic>>? dataList,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #batchInsert,
          [
            table,
            dataList,
          ],
        ),
        returnValue: _i4
            .Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>.value(
            _FakeResult_10<List<Map<String, dynamic>>, _i7.AppError>(
          this,
          Invocation.method(
            #batchInsert,
            [
              table,
              dataList,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>);
}

/// A class which mocks [RealTimeService].
///
/// See the documentation for Mockito's code generation for more information.
class MockRealTimeService extends _i1.Mock implements _i8.RealTimeService {
  MockRealTimeService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isConnected => (super.noSuchMethod(
        Invocation.getter(#isConnected),
        returnValue: false,
      ) as bool);

  @override
  _i4.Stream<bool> get connectionStream => (super.noSuchMethod(
        Invocation.getter(#connectionStream),
        returnValue: _i4.Stream<bool>.empty(),
      ) as _i4.Stream<bool>);

  @override
  List<String> get activeChannels => (super.noSuchMethod(
        Invocation.getter(#activeChannels),
        returnValue: <String>[],
      ) as List<String>);

  @override
  int get subscriptionCount => (super.noSuchMethod(
        Invocation.getter(#subscriptionCount),
        returnValue: 0,
      ) as int);

  @override
  _i4.Future<_i3.Result<_i2.RealtimeChannel, _i7.AppError>> subscribeToTable(
    String? tableName,
    void Function(_i2.PostgresChangePayload)? callback,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribeToTable,
          [
            tableName,
            callback,
          ],
        ),
        returnValue:
            _i4.Future<_i3.Result<_i2.RealtimeChannel, _i7.AppError>>.value(
                _FakeResult_10<_i2.RealtimeChannel, _i7.AppError>(
          this,
          Invocation.method(
            #subscribeToTable,
            [
              tableName,
              callback,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<_i2.RealtimeChannel, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<_i2.RealtimeChannel, _i7.AppError>> subscribeToUserData(
    String? userId,
    String? tableName,
    void Function(_i2.PostgresChangePayload)? callback,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribeToUserData,
          [
            userId,
            tableName,
            callback,
          ],
        ),
        returnValue:
            _i4.Future<_i3.Result<_i2.RealtimeChannel, _i7.AppError>>.value(
                _FakeResult_10<_i2.RealtimeChannel, _i7.AppError>(
          this,
          Invocation.method(
            #subscribeToUserData,
            [
              userId,
              tableName,
              callback,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<_i2.RealtimeChannel, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> unsubscribe(String? channelName) =>
      (super.noSuchMethod(
        Invocation.method(
          #unsubscribe,
          [channelName],
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #unsubscribe,
            [channelName],
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> unsubscribeAll() =>
      (super.noSuchMethod(
        Invocation.method(
          #unsubscribeAll,
          [],
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #unsubscribeAll,
            [],
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<_i2.RealtimeChannel, _i7.AppError>> subscribeToRecord(
    String? tableName,
    String? recordId,
    void Function(_i2.PostgresChangePayload)? callback,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #subscribeToRecord,
          [
            tableName,
            recordId,
            callback,
          ],
        ),
        returnValue:
            _i4.Future<_i3.Result<_i2.RealtimeChannel, _i7.AppError>>.value(
                _FakeResult_10<_i2.RealtimeChannel, _i7.AppError>(
          this,
          Invocation.method(
            #subscribeToRecord,
            [
              tableName,
              recordId,
              callback,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<_i2.RealtimeChannel, _i7.AppError>>);

  @override
  _i4.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}

/// A class which mocks [CacheService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCacheService extends _i1.Mock implements _i9.CacheService {
  MockCacheService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> set(
    String? key,
    dynamic data, {
    int? ttlSeconds,
    bool? useSessionStorage = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #set,
          [
            key,
            data,
          ],
          {
            #ttlSeconds: ttlSeconds,
            #useSessionStorage: useSessionStorage,
          },
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #set,
            [
              key,
              data,
            ],
            {
              #ttlSeconds: ttlSeconds,
              #useSessionStorage: useSessionStorage,
            },
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<T?, _i7.AppError>> get<T>(
    String? key, {
    bool? useSessionStorage = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [key],
          {#useSessionStorage: useSessionStorage},
        ),
        returnValue: _i4.Future<_i3.Result<T?, _i7.AppError>>.value(
            _FakeResult_10<T?, _i7.AppError>(
          this,
          Invocation.method(
            #get,
            [key],
            {#useSessionStorage: useSessionStorage},
          ),
        )),
      ) as _i4.Future<_i3.Result<T?, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> remove(
    String? key, {
    bool? useSessionStorage = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #remove,
          [key],
          {#useSessionStorage: useSessionStorage},
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #remove,
            [key],
            {#useSessionStorage: useSessionStorage},
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> clear(
          {bool? useSessionStorage = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #clear,
          [],
          {#useSessionStorage: useSessionStorage},
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #clear,
            [],
            {#useSessionStorage: useSessionStorage},
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> exists(
    String? key, {
    bool? useSessionStorage = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #exists,
          [key],
          {#useSessionStorage: useSessionStorage},
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #exists,
            [key],
            {#useSessionStorage: useSessionStorage},
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>> getStatus() =>
      (super.noSuchMethod(
        Invocation.method(
          #getStatus,
          [],
        ),
        returnValue:
            _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>.value(
                _FakeResult_10<Map<String, dynamic>, _i7.AppError>(
          this,
          Invocation.method(
            #getStatus,
            [],
          ),
        )),
      ) as _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<int, _i7.AppError>> evictExpired() =>
      (super.noSuchMethod(
        Invocation.method(
          #evictExpired,
          [],
        ),
        returnValue: _i4.Future<_i3.Result<int, _i7.AppError>>.value(
            _FakeResult_10<int, _i7.AppError>(
          this,
          Invocation.method(
            #evictExpired,
            [],
          ),
        )),
      ) as _i4.Future<_i3.Result<int, _i7.AppError>>);
}

/// A class which mocks [WebDatabaseRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebDatabaseRepository extends _i1.Mock
    implements _i10.WebDatabaseRepository {
  MockWebDatabaseRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> initialize() =>
      (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #initialize,
            [],
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>> query(
          String? table) =>
      (super.noSuchMethod(
        Invocation.method(
          #query,
          [table],
        ),
        returnValue: _i4
            .Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>.value(
            _FakeResult_10<List<Map<String, dynamic>>, _i7.AppError>(
          this,
          Invocation.method(
            #query,
            [table],
          ),
        )),
      ) as _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>> insert(
    String? table,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #insert,
          [
            table,
            data,
          ],
        ),
        returnValue:
            _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>.value(
                _FakeResult_10<Map<String, dynamic>, _i7.AppError>(
          this,
          Invocation.method(
            #insert,
            [
              table,
              data,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>> update(
    String? table,
    dynamic id,
    Map<String, dynamic>? data,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #update,
          [
            table,
            id,
            data,
          ],
        ),
        returnValue:
            _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>.value(
                _FakeResult_10<Map<String, dynamic>, _i7.AppError>(
          this,
          Invocation.method(
            #update,
            [
              table,
              id,
              data,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<Map<String, dynamic>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> delete(
    String? table,
    dynamic id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [
            table,
            id,
          ],
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #delete,
            [
              table,
              id,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<
      _i3.Result<List<Map<String, dynamic>>, _i7.AppError>> queryWithFilters(
    String? table, {
    Map<String, dynamic>? filters,
    String? orderBy,
    bool? ascending = true,
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #queryWithFilters,
          [table],
          {
            #filters: filters,
            #orderBy: orderBy,
            #ascending: ascending,
            #limit: limit,
          },
        ),
        returnValue: _i4
            .Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>.value(
            _FakeResult_10<List<Map<String, dynamic>>, _i7.AppError>(
          this,
          Invocation.method(
            #queryWithFilters,
            [table],
            {
              #filters: filters,
              #orderBy: orderBy,
              #ascending: ascending,
              #limit: limit,
            },
          ),
        )),
      ) as _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>> batchInsert(
    String? table,
    List<Map<String, dynamic>>? dataList,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #batchInsert,
          [
            table,
            dataList,
          ],
        ),
        returnValue: _i4
            .Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>.value(
            _FakeResult_10<List<Map<String, dynamic>>, _i7.AppError>(
          this,
          Invocation.method(
            #batchInsert,
            [
              table,
              dataList,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<List<Map<String, dynamic>>, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<Map<String, dynamic>?, _i7.AppError>> getById(
    String? table,
    dynamic id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getById,
          [
            table,
            id,
          ],
        ),
        returnValue:
            _i4.Future<_i3.Result<Map<String, dynamic>?, _i7.AppError>>.value(
                _FakeResult_10<Map<String, dynamic>?, _i7.AppError>(
          this,
          Invocation.method(
            #getById,
            [
              table,
              id,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<Map<String, dynamic>?, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<bool, _i7.AppError>> exists(
    String? table,
    dynamic id,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #exists,
          [
            table,
            id,
          ],
        ),
        returnValue: _i4.Future<_i3.Result<bool, _i7.AppError>>.value(
            _FakeResult_10<bool, _i7.AppError>(
          this,
          Invocation.method(
            #exists,
            [
              table,
              id,
            ],
          ),
        )),
      ) as _i4.Future<_i3.Result<bool, _i7.AppError>>);

  @override
  _i4.Future<_i3.Result<int, _i7.AppError>> count(
    String? table, {
    Map<String, dynamic>? filters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #count,
          [table],
          {#filters: filters},
        ),
        returnValue: _i4.Future<_i3.Result<int, _i7.AppError>>.value(
            _FakeResult_10<int, _i7.AppError>(
          this,
          Invocation.method(
            #count,
            [table],
            {#filters: filters},
          ),
        )),
      ) as _i4.Future<_i3.Result<int, _i7.AppError>>);
}
