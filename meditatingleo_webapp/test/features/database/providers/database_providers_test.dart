import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:meditatingleo_webapp/features/database/providers/database_providers.dart';
import 'package:meditatingleo_webapp/features/database/data/services/web_database_service.dart';
import 'package:meditatingleo_webapp/features/database/data/services/real_time_service.dart';
import 'package:meditatingleo_webapp/features/database/data/services/cache_service.dart';
import 'package:meditatingleo_webapp/features/database/data/repositories/web_database_repository.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';

import 'database_providers_test.mocks.dart';

@GenerateMocks([
  SupabaseClient,
  WebDatabaseService,
  RealTimeService,
  CacheService,
  WebDatabaseRepository,
])
void main() {
  group('Database Providers', () {
    late ProviderContainer container;
    late MockSupabaseClient mockSupabaseClient;
    late MockWebDatabaseService mockDatabaseService;
    late MockRealTimeService mockRealTimeService;
    late MockCacheService mockCacheService;
    late MockWebDatabaseRepository mockRepository;

    setUp(() {
      mockSupabaseClient = MockSupabaseClient();
      mockDatabaseService = MockWebDatabaseService();
      mockRealTimeService = MockRealTimeService();
      mockCacheService = MockCacheService();
      mockRepository = MockWebDatabaseRepository();

      container = ProviderContainer(
        overrides: [
          // Override providers with mocks for testing
          webSupabaseProvider.overrideWithValue(mockSupabaseClient),
          webDatabaseServiceProvider.overrideWithValue(mockDatabaseService),
          realTimeServiceProvider.overrideWithValue(mockRealTimeService),
          cacheServiceProvider.overrideWithValue(mockCacheService),
          webDatabaseRepositoryProvider.overrideWithValue(mockRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('webSupabaseProvider', () {
      test('should provide Supabase client instance', () {
        // Act
        final supabaseClient = container.read(webSupabaseProvider);

        // Assert
        expect(supabaseClient, isA<SupabaseClient>());
        expect(supabaseClient, equals(mockSupabaseClient));
      });
    });

    group('webDatabaseServiceProvider', () {
      test('should provide WebDatabaseService instance', () {
        // Act
        final databaseService = container.read(webDatabaseServiceProvider);

        // Assert
        expect(databaseService, isA<WebDatabaseService>());
        expect(databaseService, equals(mockDatabaseService));
      });
    });

    group('realTimeServiceProvider', () {
      test('should provide RealTimeService instance', () {
        // Act
        final realTimeService = container.read(realTimeServiceProvider);

        // Assert
        expect(realTimeService, isA<RealTimeService>());
        expect(realTimeService, equals(mockRealTimeService));
      });
    });

    group('cacheServiceProvider', () {
      test('should provide CacheService instance', () {
        // Act
        final cacheService = container.read(cacheServiceProvider);

        // Assert
        expect(cacheService, isA<CacheService>());
        expect(cacheService, equals(mockCacheService));
      });
    });

    group('webDatabaseRepositoryProvider', () {
      test('should provide WebDatabaseRepository instance', () {
        // Act
        final repository = container.read(webDatabaseRepositoryProvider);

        // Assert
        expect(repository, isA<WebDatabaseRepository>());
        expect(repository, equals(mockRepository));
      });
    });

    group('databaseInitializationProvider', () {
      test('should initialize database successfully', () async {
        // Arrange
        when(mockRepository.initialize())
            .thenAnswer((_) async => const Result.success(true));

        // Act
        final asyncValue = await container.read(databaseInitializationProvider.future);

        // Assert
        expect(asyncValue, true);
        verify(mockRepository.initialize()).called(1);
      });

      test('should handle database initialization failure', () async {
        // Arrange
        when(mockRepository.initialize())
            .thenAnswer((_) async => const Result.failure(
                AppError.database('Initialization failed')));

        // Act & Assert
        expect(
          () => container.read(databaseInitializationProvider.future),
          throwsA(isA<AppError>()),
        );
        verify(mockRepository.initialize()).called(1);
      });
    });

    group('connectionStatusProvider', () {
      test('should provide real-time connection status', () {
        // Arrange
        when(mockRealTimeService.isConnected).thenReturn(true);

        // Act
        final isConnected = container.read(connectionStatusProvider);

        // Assert
        expect(isConnected, true);
        verify(mockRealTimeService.isConnected).called(1);
      });

      test('should handle disconnected status', () {
        // Arrange
        when(mockRealTimeService.isConnected).thenReturn(false);

        // Act
        final isConnected = container.read(connectionStatusProvider);

        // Assert
        expect(isConnected, false);
        verify(mockRealTimeService.isConnected).called(1);
      });
    });

    group('cacheStatusProvider', () {
      test('should provide cache status information', () async {
        // Arrange
        final mockCacheStatus = {
          'size': 1024,
          'entries': 50,
          'hitRate': 0.85,
        };
        when(mockCacheService.getStatus())
            .thenAnswer((_) async => Result.success(mockCacheStatus));

        // Act
        final cacheStatus = await container.read(cacheStatusProvider.future);

        // Assert
        expect(cacheStatus, equals(mockCacheStatus));
        verify(mockCacheService.getStatus()).called(1);
      });

      test('should handle cache status error', () async {
        // Arrange
        when(mockCacheService.getStatus())
            .thenAnswer((_) async => const Result.failure(
                AppError.cache('Failed to get cache status')));

        // Act & Assert
        expect(
          () => container.read(cacheStatusProvider.future),
          throwsA(isA<AppError>()),
        );
        verify(mockCacheService.getStatus()).called(1);
      });
    });

    group('provider dependencies', () {
      test('should create providers with correct dependencies', () {
        // This test ensures that providers are created with the right dependencies
        // and that the dependency injection works correctly

        // Act
        final supabaseClient = container.read(webSupabaseProvider);
        final databaseService = container.read(webDatabaseServiceProvider);
        final realTimeService = container.read(realTimeServiceProvider);
        final cacheService = container.read(cacheServiceProvider);
        final repository = container.read(webDatabaseRepositoryProvider);

        // Assert
        expect(supabaseClient, isNotNull);
        expect(databaseService, isNotNull);
        expect(realTimeService, isNotNull);
        expect(cacheService, isNotNull);
        expect(repository, isNotNull);
      });
    });

    group('provider lifecycle', () {
      test('should dispose providers correctly', () {
        // Arrange
        final supabaseClient = container.read(webSupabaseProvider);
        final databaseService = container.read(webDatabaseServiceProvider);

        // Act
        container.dispose();

        // Assert
        // Verify that providers are properly disposed
        // This is important for preventing memory leaks
        expect(container.disposed, true);
      });
    });
  });
}
