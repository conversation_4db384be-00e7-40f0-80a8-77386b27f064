import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:meditatingleo_webapp/features/journal/data/repositories/journal_repository.dart';
import 'package:meditatingleo_webapp/features/journal/data/services/journal_service.dart';
import 'package:meditatingleo_webapp/features/journal/data/models/journal_entry_model.dart';
import 'package:meditatingleo_webapp/shared/models/result.dart';
import 'package:meditatingleo_webapp/shared/models/app_error.dart';

import 'journal_repository_test.mocks.dart';

@GenerateMocks([JournalService])
void main() {
  group('JournalRepository', () {
    late JournalRepository repository;
    late MockJournalService mockJournalService;

    setUp(() {
      mockJournalService = MockJournalService();
      repository = JournalRepository(mockJournalService);
    });

    group('createEntry', () {
      test('should create journal entry successfully', () async {
        // Arrange
        final entry = JournalEntryModel(
          id: '1',
          userId: 'user-123',
          title: 'Test Entry',
          content: 'Test content',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        when(mockJournalService.createEntry(entry))
            .thenAnswer((_) async => Result.success(entry));

        // Act
        final result = await repository.createEntry(entry);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.id, '1');
        expect(result.data?.title, 'Test Entry');
        verify(mockJournalService.createEntry(entry)).called(1);
      });

      test('should return error when create entry fails', () async {
        // Arrange
        final entry = JournalEntryModel(
          id: '1',
          userId: 'user-123',
          title: 'Test Entry',
          content: 'Test content',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        when(mockJournalService.createEntry(entry))
            .thenAnswer((_) async => const Result.failure(
                AppError.database('Failed to create entry')));

        // Act
        final result = await repository.createEntry(entry);

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Failed to create entry'));
        verify(mockJournalService.createEntry(entry)).called(1);
      });
    });

    group('getEntry', () {
      test('should get journal entry successfully', () async {
        // Arrange
        const entryId = '1';
        final entry = JournalEntryModel(
          id: entryId,
          userId: 'user-123',
          title: 'Test Entry',
          content: 'Test content',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        when(mockJournalService.getEntry(entryId))
            .thenAnswer((_) async => Result.success(entry));

        // Act
        final result = await repository.getEntry(entryId);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.id, entryId);
        verify(mockJournalService.getEntry(entryId)).called(1);
      });

      test('should return error when entry not found', () async {
        // Arrange
        const entryId = '1';
        when(mockJournalService.getEntry(entryId))
            .thenAnswer((_) async => const Result.failure(
                AppError.notFound('Journal entry')));

        // Act
        final result = await repository.getEntry(entryId);

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Not Found Error'));
        verify(mockJournalService.getEntry(entryId)).called(1);
      });
    });

    group('getUserEntries', () {
      test('should get user entries successfully', () async {
        // Arrange
        const userId = 'user-123';
        final entries = [
          JournalEntryModel(
            id: '1',
            userId: userId,
            title: 'Entry 1',
            content: 'Content 1',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          JournalEntryModel(
            id: '2',
            userId: userId,
            title: 'Entry 2',
            content: 'Content 2',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];
        when(mockJournalService.getUserEntries(userId))
            .thenAnswer((_) async => Result.success(entries));

        // Act
        final result = await repository.getUserEntries(userId);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.length, 2);
        expect(result.data?.first.userId, userId);
        verify(mockJournalService.getUserEntries(userId)).called(1);
      });

      test('should return empty list when user has no entries', () async {
        // Arrange
        const userId = 'user-123';
        when(mockJournalService.getUserEntries(userId))
            .thenAnswer((_) async => const Result.success(<JournalEntryModel>[]));

        // Act
        final result = await repository.getUserEntries(userId);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.isEmpty, true);
        verify(mockJournalService.getUserEntries(userId)).called(1);
      });
    });

    group('updateEntry', () {
      test('should update journal entry successfully', () async {
        // Arrange
        final entry = JournalEntryModel(
          id: '1',
          userId: 'user-123',
          title: 'Updated Entry',
          content: 'Updated content',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        when(mockJournalService.updateEntry(entry))
            .thenAnswer((_) async => Result.success(entry));

        // Act
        final result = await repository.updateEntry(entry);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.title, 'Updated Entry');
        verify(mockJournalService.updateEntry(entry)).called(1);
      });

      test('should return error when update fails', () async {
        // Arrange
        final entry = JournalEntryModel(
          id: '1',
          userId: 'user-123',
          title: 'Updated Entry',
          content: 'Updated content',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        when(mockJournalService.updateEntry(entry))
            .thenAnswer((_) async => const Result.failure(
                AppError.database('Failed to update entry')));

        // Act
        final result = await repository.updateEntry(entry);

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Failed to update entry'));
        verify(mockJournalService.updateEntry(entry)).called(1);
      });
    });

    group('deleteEntry', () {
      test('should delete journal entry successfully', () async {
        // Arrange
        const entryId = '1';
        when(mockJournalService.deleteEntry(entryId))
            .thenAnswer((_) async => const Result.success(true));

        // Act
        final result = await repository.deleteEntry(entryId);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, true);
        verify(mockJournalService.deleteEntry(entryId)).called(1);
      });

      test('should return error when delete fails', () async {
        // Arrange
        const entryId = '1';
        when(mockJournalService.deleteEntry(entryId))
            .thenAnswer((_) async => const Result.failure(
                AppError.database('Failed to delete entry')));

        // Act
        final result = await repository.deleteEntry(entryId);

        // Assert
        expect(result.isFailure, true);
        expect(result.error?.technicalMessage, contains('Failed to delete entry'));
        verify(mockJournalService.deleteEntry(entryId)).called(1);
      });
    });
  });
}
