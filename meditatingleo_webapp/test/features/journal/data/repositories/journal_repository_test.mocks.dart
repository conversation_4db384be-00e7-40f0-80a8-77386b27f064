// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in meditatingleo_webapp/test/features/journal/data/repositories/journal_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:meditatingleo_webapp/features/journal/data/models/journal_entry_model.dart'
    as _i5;
import 'package:meditatingleo_webapp/features/journal/data/services/journal_service.dart'
    as _i3;
import 'package:meditatingleo_webapp/shared/models/app_error.dart' as _i6;
import 'package:meditatingleo_webapp/shared/models/result.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResult_0<T, E> extends _i1.SmartFake implements _i2.Result<T, E> {
  _FakeResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [JournalService].
///
/// See the documentation for Mockito's code generation for more information.
class MockJournalService extends _i1.Mock implements _i3.JournalService {
  MockJournalService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Result<_i5.JournalEntryModel, _i6.AppError>> createEntry(
          _i5.JournalEntryModel? entry) =>
      (super.noSuchMethod(
        Invocation.method(
          #createEntry,
          [entry],
        ),
        returnValue:
            _i4.Future<_i2.Result<_i5.JournalEntryModel, _i6.AppError>>.value(
                _FakeResult_0<_i5.JournalEntryModel, _i6.AppError>(
          this,
          Invocation.method(
            #createEntry,
            [entry],
          ),
        )),
      ) as _i4.Future<_i2.Result<_i5.JournalEntryModel, _i6.AppError>>);

  @override
  _i4.Future<_i2.Result<_i5.JournalEntryModel?, _i6.AppError>> getEntry(
          String? entryId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getEntry,
          [entryId],
        ),
        returnValue:
            _i4.Future<_i2.Result<_i5.JournalEntryModel?, _i6.AppError>>.value(
                _FakeResult_0<_i5.JournalEntryModel?, _i6.AppError>(
          this,
          Invocation.method(
            #getEntry,
            [entryId],
          ),
        )),
      ) as _i4.Future<_i2.Result<_i5.JournalEntryModel?, _i6.AppError>>);

  @override
  _i4.Future<
      _i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>> getUserEntries(
    String? userId, {
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserEntries,
          [userId],
          {
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i4.Future<
                _i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>.value(
            _FakeResult_0<List<_i5.JournalEntryModel>, _i6.AppError>(
          this,
          Invocation.method(
            #getUserEntries,
            [userId],
            {
              #limit: limit,
              #offset: offset,
            },
          ),
        )),
      ) as _i4.Future<_i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>);

  @override
  _i4.Future<_i2.Result<_i5.JournalEntryModel, _i6.AppError>> updateEntry(
          _i5.JournalEntryModel? entry) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateEntry,
          [entry],
        ),
        returnValue:
            _i4.Future<_i2.Result<_i5.JournalEntryModel, _i6.AppError>>.value(
                _FakeResult_0<_i5.JournalEntryModel, _i6.AppError>(
          this,
          Invocation.method(
            #updateEntry,
            [entry],
          ),
        )),
      ) as _i4.Future<_i2.Result<_i5.JournalEntryModel, _i6.AppError>>);

  @override
  _i4.Future<_i2.Result<bool, _i6.AppError>> deleteEntry(String? entryId) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteEntry,
          [entryId],
        ),
        returnValue: _i4.Future<_i2.Result<bool, _i6.AppError>>.value(
            _FakeResult_0<bool, _i6.AppError>(
          this,
          Invocation.method(
            #deleteEntry,
            [entryId],
          ),
        )),
      ) as _i4.Future<_i2.Result<bool, _i6.AppError>>);

  @override
  _i4.Future<
      _i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>> searchEntries(
    String? userId,
    String? query, {
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchEntries,
          [
            userId,
            query,
          ],
          {#limit: limit},
        ),
        returnValue: _i4.Future<
                _i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>.value(
            _FakeResult_0<List<_i5.JournalEntryModel>, _i6.AppError>(
          this,
          Invocation.method(
            #searchEntries,
            [
              userId,
              query,
            ],
            {#limit: limit},
          ),
        )),
      ) as _i4.Future<_i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>);

  @override
  _i4.Future<
      _i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>> getEntriesByTags(
    String? userId,
    List<String>? tags, {
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getEntriesByTags,
          [
            userId,
            tags,
          ],
          {#limit: limit},
        ),
        returnValue: _i4.Future<
                _i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>.value(
            _FakeResult_0<List<_i5.JournalEntryModel>, _i6.AppError>(
          this,
          Invocation.method(
            #getEntriesByTags,
            [
              userId,
              tags,
            ],
            {#limit: limit},
          ),
        )),
      ) as _i4.Future<_i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>);

  @override
  _i4.Future<
      _i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>> getFavoriteEntries(
    String? userId, {
    int? limit,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getFavoriteEntries,
          [userId],
          {#limit: limit},
        ),
        returnValue: _i4.Future<
                _i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>.value(
            _FakeResult_0<List<_i5.JournalEntryModel>, _i6.AppError>(
          this,
          Invocation.method(
            #getFavoriteEntries,
            [userId],
            {#limit: limit},
          ),
        )),
      ) as _i4.Future<_i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>);

  @override
  _i4.Future<_i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>
      getEntriesInDateRange(
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  ) =>
          (super.noSuchMethod(
            Invocation.method(
              #getEntriesInDateRange,
              [
                userId,
                startDate,
                endDate,
              ],
            ),
            returnValue: _i4.Future<
                    _i2
                    .Result<List<_i5.JournalEntryModel>, _i6.AppError>>.value(
                _FakeResult_0<List<_i5.JournalEntryModel>, _i6.AppError>(
              this,
              Invocation.method(
                #getEntriesInDateRange,
                [
                  userId,
                  startDate,
                  endDate,
                ],
              ),
            )),
          ) as _i4
              .Future<_i2.Result<List<_i5.JournalEntryModel>, _i6.AppError>>);

  @override
  _i4.Future<_i2.Result<Map<String, dynamic>, _i6.AppError>> getEntryStatistics(
          String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getEntryStatistics,
          [userId],
        ),
        returnValue:
            _i4.Future<_i2.Result<Map<String, dynamic>, _i6.AppError>>.value(
                _FakeResult_0<Map<String, dynamic>, _i6.AppError>(
          this,
          Invocation.method(
            #getEntryStatistics,
            [userId],
          ),
        )),
      ) as _i4.Future<_i2.Result<Map<String, dynamic>, _i6.AppError>>);
}
