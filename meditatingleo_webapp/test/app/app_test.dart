import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'package:meditatingleo_webapp/app/app.dart';

void main() {
  group('WebApp', () {
    setUpAll(() async {
      // Initialize dotenv with test values
      dotenv.testLoad(fileInput: '''
SUPABASE_URL=https://test.supabase.co
SUPABASE_ANON_KEY=test-anon-key
WEB_APP_NAME=TestApp
DEBUG_MODE=true
RESPONSIVE_BREAKPOINT_MOBILE=600
RESPONSIVE_BREAKPOINT_TABLET=900
RESPONSIVE_BREAKPOINT_DESKTOP=1200
''');
    });

    testWidgets('should initialize and display home page', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: WebApp(),
        ),
      );

      // Should display the app without errors
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('should use Material Design 3', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: WebApp(),
        ),
      );

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(materialApp.theme?.useMaterial3, isTrue);
    });

    testWidgets('should have proper web configuration', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: WebApp(),
        ),
      );

      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));
      expect(
          materialApp.title, equals('TestApp')); // Using test environment value
    });
  });
}
